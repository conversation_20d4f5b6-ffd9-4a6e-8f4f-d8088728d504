<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.ViewArchiveTaskTodoMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.ViewArchiveTaskTodo">
    <!--@mbg.generated-->
    <!--@Table view_archive_task_todo-->
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="config_uuid" jdbcType="VARCHAR" property="configUuid" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="title" jdbcType="LONGVARCHAR" property="title" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="status_id_card" jdbcType="VARCHAR" property="statusIdCard" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    task_id, config_uuid, content, title, id_card, status_id_card, create_time
  </sql>
</mapper>