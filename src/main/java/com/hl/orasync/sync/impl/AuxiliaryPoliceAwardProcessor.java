package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.archive.domain.dto.UserBasicInfoDTO;
import com.hl.archive.domain.entity.AuxiliaryPoliceAward;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.service.AuxiliaryPoliceAwardService;
import com.hl.archive.service.UserCacheService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBzjlOld;
import com.hl.orasync.domain.VWjRyjbxxFjxx;
import com.hl.orasync.service.VWjBzjlOldService;
import com.hl.orasync.sync.AbstractDataSyncProcessor;
import io.github.linpeilie.Converter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Function;

@Component
public class AuxiliaryPoliceAwardProcessor extends AbstractDataSyncProcessor<VWjBzjlOld, AuxiliaryPoliceAward> {

    private final AuxiliaryPoliceAwardService auxiliaryPoliceAwardService;

    private final VWjBzjlOldService vwjBzjlOldService;

    @Resource
    private UserCacheService userCacheService;

    public AuxiliaryPoliceAwardProcessor(Converter converter, AuxiliaryPoliceAwardService auxiliaryPoliceAwardService, VWjBzjlOldService vwjBzjlOldService) {
        super(converter);
        this.auxiliaryPoliceAwardService = auxiliaryPoliceAwardService;
        this.vwjBzjlOldService = vwjBzjlOldService;
    }

    @Override
    protected IService<VWjBzjlOld> getSourceService() {
        return vwjBzjlOldService;
    }

    @Override
    protected IService<AuxiliaryPoliceAward> getTargetService() {
        return auxiliaryPoliceAwardService;
    }

    @Override
    protected Class<AuxiliaryPoliceAward> getTargetClass() {
        return AuxiliaryPoliceAward.class;
    }

    @Override
    public Function<AuxiliaryPoliceAward, String> getBusinessKeyGenerator() {
        return AuxiliaryPoliceAward::getZjid;
    }


    @Override
    public List<VWjBzjlOld> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE4));
        try {
            Page<VWjBzjlOld> page = vwjBzjlOldService.page(Page.of(offset, limit), buildSourceQueryWrapper());
            return page.getRecords();
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }


    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE4));
        try {
            return getSourceService().count(buildSourceQueryWrapper());
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    @Override
    public void batchInsert(List<AuxiliaryPoliceAward> records) {
        records.forEach(record -> {
            UserBasicInfoDTO userByIdCard = userCacheService.getUserByIdCard(record.getIdCard());
            if (userByIdCard == null) {
                record.setIsDeleted((byte) 1);
            }
        });
        super.batchInsert(records);
    }


    @Override
    public void batchUpdate(List<AuxiliaryPoliceAward> records) {
        records.forEach(record -> {
            UserBasicInfoDTO userByIdCard = userCacheService.getUserByIdCard(record.getIdCard());
            if (userByIdCard == null) {
                record.setIsDeleted((byte) 1);
            }
        });
        super.batchUpdate(records);
    }
}
