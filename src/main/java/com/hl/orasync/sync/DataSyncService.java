package com.hl.orasync.sync;

import com.aizuda.snailjob.common.log.SnailJobLog;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.log.LogSql;
import com.hl.orasync.annotation.ChangeMarkField;
import com.hl.orasync.annotation.ExtensionField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataSyncService {

    private final ExecutorService executorService;

    public DataSyncService() {
        this.executorService = Executors.newFixedThreadPool(4);
    }

    /**
     * 执行数据同步 - 全量比对模式
     */
    public <S, T> SyncResult syncData(DataSyncProcessor<S, T> processor, SyncConfig config) {
        SyncResult result = SyncResult.builder().build();
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始数据同步");
            // 1. 分页加载所有源数据
            Map<String, T> allSourceDataMap = loadAllSourceData(processor, config);
            // 2. 分页加载所有目标数据
            Map<String, T> allTargetDataMap = loadAllTargetData(processor, config);
            // 3. 全量比对数据
            DataCompareResult<T> compareResult = compareAllData(
                    allSourceDataMap, allTargetDataMap, processor.getBusinessKeyGenerator());

            log.info("数据比对完成 - 新增: {}, 更新: {}, 删除: {}",
                    compareResult.getToInsert().size(),
                    compareResult.getToUpdate().size(),
                    compareResult.getToDelete().size());

            // 4. 执行批量数据变更
            if (compareResult.hasChanges()) {
                executeBatchOperations(processor, config,
                        compareResult.getToInsert(),
                        compareResult.getToUpdate(),
                        compareResult.getToDelete());
            }

            long executionTime = System.currentTimeMillis() - startTime;
            result = SyncResult.builder()
                    .insertCount(compareResult.getToInsert().size())
                    .updateCount(compareResult.getToUpdate().size())
                    .deleteCount(compareResult.getToDelete().size())
                    .executionTime(executionTime)
                    .success(true)
                    .build();

            log.info("数据同步完成: 新增={}, 更新={}, 删除={}, 耗时={}ms",
                    result.getInsertCount(), result.getUpdateCount(),
                    result.getDeleteCount(), executionTime);

        } catch (Exception e) {
            log.error("数据同步失败", e);
            result = SyncResult.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .executionTime(System.currentTimeMillis() - startTime)
                    .build();
        }

        return result;
    }

    /**
     * 分页加载所有源数据
     */
    private <S, T> Map<String, T> loadAllSourceData(DataSyncProcessor<S, T> processor, SyncConfig config) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long totalCount = processor.getSourceDataCount();

        int totalPages = (int) Math.ceil((double) totalCount / config.getBatchSize());

        Map<String, T> allDataMap = new ConcurrentHashMap<>();
        Function<T, String> keyGenerator = processor.getBusinessKeyGenerator();

        if (config.isEnableParallelProcessing()) {
            // 并行加载
            List<Future<Void>> futures = new ArrayList<>();

            for (int page = 0; page < totalPages; page++) {
                final int currentPage = page;
                final int offset = page * config.getBatchSize();

                Future<Void> future = executorService.submit(() -> {
                    List<S> sourceData = processor.getSourceData(currentPage + 1, config.getBatchSize());
                    List<T> convertedData = sourceData.stream()
                            .map(processor::convert)
                            .collect(Collectors.toList());

                    // 添加到总的数据映射中
                    convertedData.forEach(data -> {
                        String key = keyGenerator.apply(data);
                        if (allDataMap.containsKey(key)) {
                            log.error("源数据存在异常：{}", data);
                        }
                        allDataMap.put(key, data);
                    });

                    log.debug("源数据第 {} 页加载完成，本页数据: {} 条", currentPage + 1, convertedData.size());
                    return null;
                });

                futures.add(future);
            }

            // 等待所有分页加载完成
            futures.forEach(future -> {
                try {
                    future.get();
                } catch (Exception e) {
                    throw new RuntimeException("源数据加载失败", e);
                }
            });

        } else {
            // 串行加载
            for (int page = 0; page < totalPages; page++) {

                List<S> sourceData = processor.getSourceData(page + 1, config.getBatchSize());
                List<T> convertedData = sourceData.stream()
                        .map(processor::convert)
                        .collect(Collectors.toList());

                convertedData.forEach(data -> {
                    String key = keyGenerator.apply(data);
                    if (allDataMap.containsKey(key)) {
                        log.error("源数据存在异常：{}", data);
                    }
                    allDataMap.put(key, data);
                });
                log.debug("源数据第 {} 页加载完成，本页数据: {} 条", page + 1, convertedData.size());
            }
        }
        DynamicDataSourceContextHolder.clearDataSourceType();
        SnailJobLog.REMOTE.info("源数据总条数：{} ,加载成功：{}", totalCount, allDataMap.size());
        return allDataMap;
    }

    /**
     * 分页加载所有目标数据
     */
    @LogSql
    private <S, T> Map<String, T> loadAllTargetData(DataSyncProcessor<S, T> processor, SyncConfig config) {
        long totalCount = processor.getTargetDataCount();
        int totalPages = (int) Math.ceil((double) totalCount / config.getBatchSize());

        Map<String, T> allDataMap = new ConcurrentHashMap<>();
        Function<T, String> keyGenerator = processor.getBusinessKeyGenerator();

        if (config.isEnableParallelProcessing()) {
            // 并行加载
            List<Future<Void>> futures = new ArrayList<>();

            for (int page = 0; page < totalPages; page++) {
                final int currentPage = page;
                final int offset = page * config.getBatchSize();

                Future<Void> future = executorService.submit(() -> {
                    List<T> targetData = processor.getTargetData(currentPage + 1, config.getBatchSize());

                    targetData.forEach(data -> {
                        String key = keyGenerator.apply(data);
                        if (allDataMap.containsKey(key)) {
                            log.error("目标数据数据存在异常：{}", data);
                        }
                        allDataMap.put(key, data);
                    });

                    log.debug("目标数据第 {} 页加载完成，本页数据: {} 条", currentPage + 1, targetData.size());
                    return null;
                });

                futures.add(future);
            }

            // 等待所有分页加载完成
            futures.forEach(future -> {
                try {
                    future.get();
                } catch (Exception e) {
                    throw new RuntimeException("目标数据加载失败", e);
                }
            });

        } else {
            // 串行加载
            for (int page = 0; page < totalPages; page++) {
                int offset = page * config.getBatchSize();
                List<T> targetData = processor.getTargetData(page + 1, config.getBatchSize());

                targetData.forEach(data -> {
                    String key = keyGenerator.apply(data);
                    if (allDataMap.containsKey(key)) {
                        log.error("目标数据数据存在异常：{}", data);
                    }
                    allDataMap.put(key, data);
                });

                log.debug("目标数据第 {} 页加载完成，本页数据: {} 条", page + 1, targetData.size());
            }
        }
        SnailJobLog.REMOTE.info("目标数据总条数：{} ,加载成功：{}", totalCount, allDataMap.size());
        return allDataMap;
    }

    /**
     * 全量数据比对算法
     */
    private <T> DataCompareResult<T> compareAllData(
            Map<String, T> sourceDataMap,
            Map<String, T> targetDataMap,
            Function<T, String> keyGenerator) {

        List<T> toInsert = new ArrayList<>();
        List<T> toUpdate = new ArrayList<>();
        List<String> toDelete = new ArrayList<>();

        // 1. 遍历源数据，找出新增和更新的记录
        sourceDataMap.forEach((key, sourceRecord) -> {
            T targetRecord = targetDataMap.get(key);
            if (targetRecord == null) {
                // 源数据存在，目标数据不存在 -> 新增
                toInsert.add(sourceRecord);
            } else {
                // 源数据和目标数据都存在，比较内容是否相同
                if (!isRecordEqual(sourceRecord, targetRecord)) {
                    // 内容不同 -> 更新
                    // 合并数据：保留目标记录的扩展字段，更新源数据字段
                    T mergedRecord = mergeRecords(sourceRecord, targetRecord);
                    toUpdate.add(mergedRecord);
                }
                // 内容相同 -> 无需处理
            }
        });

        // 2. 遍历目标数据，找出需要删除的记录
        targetDataMap.forEach((key, targetRecord) -> {
            if (!sourceDataMap.containsKey(key)) {
                // 目标数据存在，源数据不存在 -> 删除
                Object o = copyPrimaryKey(targetRecord);
                if (o != null) {
                    toDelete.add(o.toString());
                }
            }
        });

        return DataCompareResult.<T>builder()
                .toInsert(toInsert)
                .toUpdate(toUpdate)
                .toDelete(toDelete)
                .build();
    }

    /**
     * 比较两个记录是否相等
     * 使用实体类重写的 equals 方法进行比较
     */
    private <T> boolean isRecordEqual(T record1, T record2) {
        return Objects.equals(record1, record2);
    }

    /**
     * 合并记录：将源记录的数据更新到目标记录中，保留目标记录的扩展字段
     * @param sourceRecord 源记录（来自源数据库）
     * @param targetRecord 目标记录（来自目标数据库，包含扩展字段）
     * @return 合并后的记录
     */
    private <T> T mergeRecords(T sourceRecord, T targetRecord) {
        try {

            Class<?> targetRecordClass = targetRecord.getClass();
            if (targetRecordClass.isAnnotationPresent(ChangeMarkField.class)){
                ChangeMarkField annotation = targetRecordClass.getAnnotation(ChangeMarkField.class);
                String value = annotation.value();
                Field field = targetRecordClass.getDeclaredField(value);
                field.setAccessible(true);
                Object filedValue = field.get(targetRecord);
                if (filedValue != null) {
                    if (filedValue instanceof Number) {
                        boolean skip = ((Number) filedValue).intValue() == 1;
                        if (skip) {
                            log.info("字段：{} 检验，跳过。描述：{}", value, annotation.description());
                            return targetRecord;
                        }
                    }
                }
            }

            // 获取所有字段
            Field[] fields = sourceRecord.getClass().getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);

                // 跳过主键字段和扩展字段
                if (isExtensionField(field)) {
                    continue;
                }

                // 从源记录获取值并设置到目标记录
                Object sourceValue = field.get(sourceRecord);
                field.set(targetRecord, sourceValue);
            }

            return targetRecord;
        } catch (Exception e) {
            log.warn("合并记录失败，使用源记录", e);
            // 如果合并失败，回退到原来的逻辑
            copyPrimaryKey(targetRecord, sourceRecord);
            return sourceRecord;
        }
    }

    /**
     * 判断是否为扩展字段（需要保留的字段）
     * 可以通过注解、字段名称或其他规则来判断
     */
    private boolean isExtensionField(Field field) {
        String fieldName = field.getName();
        Class<?> declaringClass = field.getDeclaringClass();

        // 跳过主键字段
        if (field.isAnnotationPresent(TableId.class)) {
            return true;
        }

//        // 跳过不存在于数据库的字段
//        if (field.isAnnotationPresent(TableField.class)) {
//            TableField tableField = field.getAnnotation(TableField.class);
//            if (!tableField.exist()) {
//                return true;
//            }
//        }

        // 检查是否有扩展字段注解
        if (field.isAnnotationPresent(ExtensionField.class)) {
            return true;
        }



        return false;
    }

    /**
     * 执行批量操作
     */
    private <S, T> void executeBatchOperations(
            DataSyncProcessor<S, T> processor,
            SyncConfig config,
            List<T> toInsert,
            List<T> toUpdate,
            List<String> toDelete) {

        // 批量插入
        if (!toInsert.isEmpty()) {
            log.info("开始批量插入 {} 条记录", toInsert.size());
            processBatch(toInsert, config.getBatchSize(), processor::batchInsert);
            log.info("批量插入完成");
        }

        // 批量更新
        if (!toUpdate.isEmpty()) {
            log.info("开始批量更新 {} 条记录", toUpdate.size());
            processBatch(toUpdate, config.getBatchSize(), processor::batchUpdate);
            log.info("批量更新完成");
        }

        // 批量删除
        if (!toDelete.isEmpty()) {
            log.info("开始批量删除 {} 条记录", toDelete.size());
            processBatch(toDelete, config.getBatchSize(), processor::batchDelete);
            log.info("批量删除完成");
        }
    }

    /**
     * 分批处理
     */
    private <T> void processBatch(List<T> data, int batchSize, Consumer<List<T>> processor) {
        for (int i = 0; i < data.size(); i += batchSize) {
            int end = Math.min(i + batchSize, data.size());
            List<T> batch = data.subList(i, end);
            processor.accept(batch);
            log.debug("处理批次: {} - {}", i + 1, end);
        }
    }

    @PreDestroy
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private <T> Object copyPrimaryKey(T targetRecord) {
        try {
            // 使用反射获取主键字段（通常是id字段）
            Field idField = findIdField(targetRecord.getClass());
            if (idField != null) {
                idField.setAccessible(true);
                Object idValue = idField.get(targetRecord);
                return idValue;
            }
        } catch (Exception e) {
            log.warn("复制主键失败", e);
        }
        return null;
    }


    private <T> void copyPrimaryKey(T targetRecord, T sourceRecord) {
        try {
            // 使用反射获取主键字段（通常是id字段）
            Field idField = findIdField(targetRecord.getClass());
            if (idField != null) {
                idField.setAccessible(true);
                Object idValue = idField.get(targetRecord);
                idField.set(sourceRecord, idValue);
            }
        } catch (Exception e) {
            log.warn("复制主键失败", e);
        }
    }

    /**
     * 查找主键字段
     */
    private Field findIdField(Class<?> clazz) {
        // 优先查找带有@TableId注解的字段
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(TableId.class)) {
                return field;
            }
        }

        // 如果没有@TableId注解，查找名为"id"的字段
        try {
            return clazz.getDeclaredField("id");
        } catch (NoSuchFieldException e) {
            log.warn("未找到主键字段: {}", clazz.getName());
            return null;
        }
    }


}
