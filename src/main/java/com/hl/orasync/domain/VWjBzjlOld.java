package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.AuxiliaryPoliceAward;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "FJXX.V_WJ_BZJL_OLD")
@Data
@TableName(value = "FJXX.V_WJ_BZJL_OLD")
@AutoMapper(target = AuxiliaryPoliceAward.class, uses = {ConversionUtils.class})
public class VWjBzjlOld {
    /**
     * 主键编号
     */
    @TableField(value = "ID")
    @ApiModelProperty(value = "主键编号")
    @AutoMapping(target = "id",ignore = true)
    private String zjid;

    /**
     * 人员主键编号
     */
    @TableField(value = "PERSONID")
    @ApiModelProperty(value = "人员主键编号")
    @AutoMapping(target = "personId")
    private String personid;

    /**
     * 身份证号
     */
    @TableField(value = "GMSFHM")
    @ApiModelProperty(value = "身份证号")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    /**
     * 奖惩名称
     */
    @TableField(value = "JCMC")
    @ApiModelProperty(value = "奖惩名称")
    @AutoMapping(target = "awardName")
    private String jcmc;

    /**
     * 奖惩类别
     */
    @TableField(value = "JCLB")
    @ApiModelProperty(value = "奖惩类别")
    @AutoMapping(target = "awardType")
    private String jclb;

    /**
     * 奖惩等级
     */
    @TableField(value = "JCDJ")
    @ApiModelProperty(value = "奖惩等级")
    @AutoMapping(target = "awardLevel")
    private String jcdj;

    /**
     * 奖惩时间
     */
    @TableField(value = "JCSJ")
    @ApiModelProperty(value = "奖惩时间")
    @AutoMapping(target = "awardTime", qualifiedByName = "strToLocalDate")
    private String jcsj;

    /**
     * 批准机关
     */
    @TableField(value = "DWMC")
    @ApiModelProperty(value = "批准机关")
    @AutoMapping(target = "awardOrg")
    private String dwmc;

    /**
     * 奖惩文号
     */
    @TableField(value = "JCBH")
    @ApiModelProperty(value = "奖惩文号")
    @AutoMapping(target = "awardBh")
    private String jcbh;
}