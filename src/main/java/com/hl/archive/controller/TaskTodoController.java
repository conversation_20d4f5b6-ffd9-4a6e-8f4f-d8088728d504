package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.ViewArchiveTaskTodo;
import com.hl.archive.domain.dto.TaskTodoQueryDTO;
import com.hl.archive.service.ViewArchiveTaskTodoService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/taskTodo")
@Api(tags = "任务待办列表")
@RequiredArgsConstructor
public class TaskTodoController {

    private final ViewArchiveTaskTodoService viewArchiveTaskTodoService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<ViewArchiveTaskTodo>> page(@RequestBody TaskTodoQueryDTO queryDTO) {
        Page<ViewArchiveTaskTodo> viewArchiveTaskTodoPage = viewArchiveTaskTodoService.pageList(queryDTO);
        return R.ok(viewArchiveTaskTodoPage.getRecords(), (int) viewArchiveTaskTodoPage.getTotal());
    }
}
