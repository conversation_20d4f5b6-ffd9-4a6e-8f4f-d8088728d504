package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.domain.dto.UserBasicInfoDTO;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 辅警表彰奖励
 */
@ApiModel(description="辅警表彰奖励")
@Data
@TableName(value = "auxiliary_police_award")
public class AuxiliaryPoliceAward {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证号")
    private String idCard;

    @TableField(exist = false)
    @Translation(type = TransConstants.USER_INFO_ID_CARD,mapper = "idCard")
    private UserBasicInfoDTO personInfo;

    /**
     * 奖惩名称
     */
    @TableField(value = "award_name")
    @ApiModelProperty(value="奖惩名称")
    private String awardName;

    /**
     * 奖惩类别
     */
    @TableField(value = "award_type")
    @ApiModelProperty(value="奖惩类别")
    private String awardType;

    /**
     * 奖惩等级
     */
    @TableField(value = "award_level")
    @ApiModelProperty(value="奖惩等级")
    private String awardLevel;

    /**
     * 奖惩日期
     */
    @TableField(value = "award_time")
    @ApiModelProperty(value="奖惩日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate awardTime;

    /**
     * 奖惩单位
     */
    @TableField(value = "award_org")
    @ApiModelProperty(value="奖惩单位")
    private String awardOrg;

    /**
     * 奖惩编号
     */
    @TableField(value = "award_bh")
    @ApiModelProperty(value="奖惩编号")
    private String awardBh;

    /**
     * 人员id
     */
    @TableField(value = "person_id")
    @ApiModelProperty(value="人员id")
    private String personId;

    /**
     * 政工主键id
     */
    @TableField(value = "zjid")
    @ApiModelProperty(value="政工主键id")
    private String zjid;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value="创建人")
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value="更新人")
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;
}