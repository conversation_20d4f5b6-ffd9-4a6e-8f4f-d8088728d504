package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

import lombok.Data;

@ApiModel(description="view_archive_task_todo")
@Data
@TableName(value = "view_archive_task_todo")
public class ViewArchiveTaskTodo {
    /**
     * 任务编号
     */
    @TableField(value = "task_id")
    @ApiModelProperty(value="任务编号")
    private String taskId;

    @TableField(value = "config_uuid")
    @ApiModelProperty(value="")
    private String configUuid;

    /**
     * 内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value="内容")
    private String content;

    /**
     * 标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value="标题")
    private String title;

    /**
     * 创建人员（如果系统字段，为空）
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="创建人员（如果系统字段，为空）")
    private String idCard;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ,mapper = "idCard")
    private JSONObject createUserInfo;

    /**
     * 当前人员
     */
    @TableField(value = "status_id_card")
    @ApiModelProperty(value="当前人员")
    private String statusIdCard;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(exist = false)
    private String configName;
}