package com.hl.archive.domain.entity;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.orasync.annotation.ChangeMarkField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 辅警信息表
 */
@ApiModel(description = "辅警信息表")
@Data
@TableName(value = "auxiliary_police_info")
@ChangeMarkField(value = "changeStatus",description = "辅警变更")
public class AuxiliaryPoliceInfo {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "")
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelIgnore
    private Long id;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 曾用名
     */
    @TableField(value = "former_name")
    @ApiModelProperty(value = "曾用名")
    @ExcelProperty(value = "曾用名")
    private String formerName;

    /**
     * 单位名称
     */
    @TableField(value = "organization")
    @ApiModelProperty(value = "单位名称")
    @ExcelProperty(value = "单位名称")
    private String organization;

    /**
     * 性别
     */
    @TableField(value = "gender")
    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别")
    private String gender;

    /**
     * 层级
     */
    @TableField(value = "hierarchy_level")
    @ApiModelProperty(value = "层级")
    @ExcelProperty(value = "层级")
    private String hierarchyLevel;

    /**
     * 出生日期
     */
    @TableField(value = "birth_date")
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "出生日期")
    private LocalDate birthDate;

    /**
     * 任职状态
     */
    @TableField(value = "employment_status")
    @ApiModelProperty(value = "任职状态")
    @ExcelProperty(value = "任职状态")
    private String employmentStatus;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号")
    private String idCard;

    /**
     * 参加辅警工作时间
     */
    @TableField(value = "start_auxiliary_date")
    @ApiModelProperty(value = "参加辅警工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "参加辅警工作时间")
    private LocalDate startAuxiliaryDate;

    /**
     * 年龄
     */
    @TableField(value = "age")
    @ApiModelProperty(value = "年龄")
    @ExcelProperty(value = "年龄")
    private String age;

    /**
     * 岗位
     */
    @TableField(value = "`position`")
    @ApiModelProperty(value = "岗位")
    @ExcelProperty(value = "岗位")
    private String position;

    /**
     * 学历
     */
    @TableField(value = "education_level")
    @ApiModelProperty(value = "学历")
    @ExcelProperty(value = "学历")
    private String educationLevel;

    /**
     * 婚姻状况
     */
    @TableField(value = "marital_status")
    @ApiModelProperty(value = "婚姻状况")
    @ExcelProperty(value = "婚姻状况")
    private String maritalStatus;

    /**
     * 政治面貌
     */
    @TableField(value = "political_status")
    @ApiModelProperty(value = "政治面貌")
    @ExcelProperty(value = "政治面貌")
    private String politicalStatus;

    /**
     * 入党时间
     */
    @TableField(value = "party_join_date")
    @ApiModelProperty(value = "入党时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "入党时间")
    private LocalDate partyJoinDate;

    /**
     * 首次辅助时间
     */
    @TableField(value = "first_auxiliary_date")
    @ApiModelProperty(value = "首次辅助时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "首次辅助时间")
    private LocalDate firstAuxiliaryDate;

    /**
     * 首次工作时间
     */
    @TableField(value = "first_work_date")
    @ApiModelProperty(value = "首次工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "首次工作时间")
    private LocalDate firstWorkDate;

    /**
     * 籍贯
     */
    @TableField(value = "native_place")
    @ApiModelProperty(value = "籍贯")
    @ExcelProperty(value = "籍贯")
    private String nativePlace;

    /**
     * 户籍地址
     */
    @TableField(value = "registered_address")
    @ApiModelProperty(value = "户籍地址")
    @ExcelProperty(value = "户籍地址")
    private String registeredAddress;

    /**
     * 工号
     */
    @TableField(value = "employee_number")
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号")
    private String employeeNumber;

    /**
     * 居住地
     */
    @TableField(value = "residence_address")
    @ApiModelProperty(value = "居住地")
    @ExcelProperty(value = "居住地")
    private String residenceAddress;

    /**
     * 劳务公司
     */
    @TableField(value = "labor_company")
    @ApiModelProperty(value = "劳务公司")
    @ExcelProperty(value = "劳务公司")
    private String laborCompany;

    /**
     * 保障渠道
     */
    @TableField(value = "security_channel")
    @ApiModelProperty(value = "保障渠道")
    @ExcelProperty(value = "保障渠道")
    private String securityChannel;

    /**
     * 手机号码
     */
    @TableField(value = "phone_number")
    @ApiModelProperty(value = "手机号码")
    @ExcelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 技术等级/职称
     */
    @TableField(value = "professional_title")
    @ApiModelProperty(value = "技术等级/职称")
    @ExcelProperty(value = "技术等级/职称")
    private String professionalTitle;

    /**
     * 座机内线
     */
    @TableField(value = "office_phone_inner")
    @ApiModelProperty(value = "座机内线")
    @ExcelProperty(value = "座机内线")
    private String officePhoneInner;

    /**
     * 座机外线
     */
    @TableField(value = "office_phone_outer")
    @ApiModelProperty(value = "座机外线")
    @ExcelProperty(value = "座机外线")
    private String officePhoneOuter;

    /**
     * 血型
     */
    @TableField(value = "blood_type")
    @ApiModelProperty(value = "血型")
    @ExcelProperty(value = "血型")
    private String bloodType;

    /**
     * 辅助工龄
     */
    @TableField(value = "auxiliary_seniority")
    @ApiModelProperty(value = "辅助工龄")
    @ExcelProperty(value = "辅助工龄")
    private String auxiliarySeniority;

    /**
     * 责任领导警号
     */
    @TableField(value = "leader_police_number")
    @ApiModelProperty(value = "责任领导警号")
    @ExcelProperty(value = "责任领导警号")
    private String leaderPoliceNumber;

    /**
     * 责任领导姓名
     */
    @TableField(value = "leader_name")
    @ApiModelProperty(value = "责任领导姓名")
    @ExcelProperty(value = "责任领导姓名")
    private String leaderName;

    /**
     * 健康状况
     */
    @TableField(value = "health_status")
    @ApiModelProperty(value = "健康状况")
    @ExcelProperty(value = "健康状况")
    private String healthStatus;

    /**
     * 重大病史
     */
    @TableField(value = "medical_history")
    @ApiModelProperty(value = "重大病史")
    @ExcelProperty(value = "重大病史")
    private String medicalHistory;

    /**
     * 是否服兵役
     */
    @TableField(value = "military_service")
    @ApiModelProperty(value = "是否服兵役")
    @ExcelProperty(value = "是否服兵役")
    private String militaryService;

    /**
     * 驾照
     */
    @TableField(value = "driver_license")
    @ApiModelProperty(value = "驾照")
    @ExcelProperty(value = "驾照")
    private String driverLicense;

    /**
     * 专业特长
     */
    @TableField(value = "specialty")
    @ApiModelProperty(value = "专业特长")
    @ExcelProperty(value = "专业特长")
    private String specialty;

    /**
     * 民族
     */
    @TableField(value = "ethnicity")
    @ApiModelProperty(value = "民族")
    @ExcelProperty(value = "民族")
    private String ethnicity;


    @TableField(value = "source_type")
    @ApiModelProperty(value = "数据来源类型")
    @ExcelIgnore
    private Integer sourceType;

    @TableField(value = "change_status")
    @ApiModelProperty(value = "变化状态")
    @ExcelIgnore
    private Integer changeStatus;

    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @ExcelIgnore
    private Byte isDeleted;

    @TableField(value = "create_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private LocalDateTime createdAt;

    @TableField(value = "update_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private LocalDateTime updatedAt;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人身份证号")
    @ExcelIgnore
    private String createdBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人身份证号")
    @ExcelIgnore
    private String updatedBy;


    @TableField(value = "organization_id")
    @ExcelIgnore
    private String organizationId;
}