package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.domain.entity.PoliceMomentSubmissionVideo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PoliceMomentSubmissionAddDTO {

    /**
     * 出境民警
     */
    private List<String> officerName;

    /**
     * 材料类型
     */
    private String materialType;

    /**
     * 报送类型
     */
    private String submissionType;

    /**
     * 报送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submissionTime;

    /**
     * 素材简介
     */
    private String materialIntro;


    private Integer dataType;

    @TableField(exist = false)
    private List<PoliceMomentSubmissionVideo> fileList;

    private String title;

    private JSONArray lawVideo;
}
