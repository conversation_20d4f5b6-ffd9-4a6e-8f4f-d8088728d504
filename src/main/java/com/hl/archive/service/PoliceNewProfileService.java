package com.hl.archive.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.FastExcel;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.*;
import com.hl.archive.domain.entity.PoliceClubActivity;
import com.hl.archive.domain.entity.PoliceNewProfile;
import com.hl.archive.domain.entity.PoliceResume;
import com.hl.archive.enums.NewPoliceRankingTypeEnum;
import com.hl.archive.enums.PoliceEventTypeEnum;
import com.hl.archive.mapper.PoliceNewProfileMapper;
import com.hl.archive.search.document.ViewCaseInfoTaskDocument;
import com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceNewProfileService extends ServiceImpl<PoliceNewProfileMapper, PoliceNewProfile> {

    private final ViewCaseInfoTaskDocumentMapper viewCaseInfoTaskDocumentMapper;

    private final PoliceClubActivityService policeClubActivityService;

    private final PoliceResumeService policeResumeService;

    public Page<PoliceNewProfile> pageList(PoliceNewProfileQueryDTO requestDTO) {
        LambdaQueryWrapper<PoliceNewProfile> queryWrapper = Wrappers.<PoliceNewProfile>lambdaQuery();
        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(requestDTO.getOrganizationId())) {
                // 非320412000000时，截取前8位
                requestDTO.setOrganizationId(requestDTO.getOrganizationId().substring(0, 8));
                queryWrapper.like(PoliceNewProfile::getOrganizationId, requestDTO.getOrganizationId());
            }
        }
        if (StrUtil.isNotBlank(requestDTO.getQuery())) {
            queryWrapper.like(PoliceNewProfile::getName, requestDTO.getQuery());
        }
        if (StrUtil.isNotBlank(requestDTO.getIdCard())) {
            queryWrapper.eq(PoliceNewProfile::getIdCard, requestDTO.getIdCard());
        }
        if (requestDTO.getOnboardDateStart() != null) {
            queryWrapper.ge(PoliceNewProfile::getOnboardDate, requestDTO.getOnboardDateStart());
        }
        if (requestDTO.getOnboardDateEnd() != null) {
            queryWrapper.le(PoliceNewProfile::getOnboardDate, requestDTO.getOnboardDateEnd());
        }
        queryWrapper.eq(StrUtil.isNotBlank(requestDTO.getUnitLeader()), PoliceNewProfile::getUnitLeader, requestDTO.getUnitLeader())
                .eq(StrUtil.isNotBlank(requestDTO.getRoleModel()), PoliceNewProfile::getRoleModel, requestDTO.getRoleModel());
        Page<PoliceNewProfile> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        // 检验标识
        checkMark(page.getRecords());
        return page;
    }


    private void checkMark(List<PoliceNewProfile> policeNewProfiles) {
        String card = UserUtils.getUser().getIdCard();
        for (PoliceNewProfile newProfile : policeNewProfiles) {
            String idCard = newProfile.getIdCard();
            if (card.equals(idCard)) {
                newProfile.setIsNewPolice(true);
            }
            String roleModel = newProfile.getRoleModel();
            if (card.equals(roleModel)) {
                newProfile.setIsRoleModel(true);
            }
        }
    }

    /**
     * 导出新警画像数据到Excel
     *
     * @param dto      查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportPoliceNewProfile(PoliceNewProfileQueryDTO dto, HttpServletResponse response) throws IOException {
        // 设置查询条件为导出所有数据
        dto.setLimit(Integer.MAX_VALUE);
        Page<PoliceNewProfile> page = this.pageList(dto);
        List<PoliceNewProfile> records = page.getRecords();

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // 使用FastExcel导出
        FastExcel.write(response.getOutputStream(), PoliceNewProfile.class)
                .autoCloseStream(Boolean.FALSE)
                .sheet("新警画像信息导出")
                .doWrite(records);
    }

    public PoliceNewProfileRoleReturnDTO checkRole() {
        List<PoliceNewProfile> list = this.list();
        String idCard = UserUtils.getUser().getIdCard();
        PoliceNewProfileRoleReturnDTO returnDTO = new PoliceNewProfileRoleReturnDTO();
        for (PoliceNewProfile policeNewProfile : list) {

            if (idCard.equals(policeNewProfile.getIdCard())) {
                returnDTO.setIsNewPolice(true);
            }
            if (idCard.equals(policeNewProfile.getUnitLeader())) {
                returnDTO.setIsUnitLeader(true);
            }
            if (idCard.equals(policeNewProfile.getRoleModel())) {
                returnDTO.setIsRoleModel(true);
            }
        }
        return returnDTO;
    }

    public void exportPoliceNewProfileWord(PoliceNewProfileQueryDTO requestDTO, HttpServletResponse response) {

        try {
            String idCard = requestDTO.getIdCard();
            PoliceNewProfile one = this.getOne(Wrappers.<PoliceNewProfile>lambdaQuery().eq(PoliceNewProfile::getIdCard, idCard));
            PoliceNewProfileExportDTO exportDTO = BeanUtil.copyProperties(one, PoliceNewProfileExportDTO.class);
            exportDTO.setUnitLeaderName(SsoCacheUtil.getUserNameByIdCard(one.getUnitLeader()));
            exportDTO.setRoleModelName(SsoCacheUtil.getUserNameByIdCard(one.getRoleModel()));
            exportDTO.setBirthDate(IdcardUtil.getBirthDate(one.getIdCard()).toDateStr());

            XWPFTemplate template = XWPFTemplate.compile("conf/template/new_police_export.docx")
                    .render(exportDTO);
            response.setContentType("application/octet-stream");
            String fileName = URLEncoder.encode("新警画像", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            template.write(bos);
            bos.flush();
            out.flush();
            PoitlIOUtils.closeQuietlyMulti(template, bos, out);
        } catch (IOException e) {
            log.error("导出Word失败", e);
        }
    }

    public void exportPoliceListWord(PoliceNewProfileQueryDTO requestDTO, HttpServletResponse response) {
        try {
            // 设置查询条件为导出所有数据
            requestDTO.setLimit(Integer.MAX_VALUE);
            Page<PoliceNewProfile> page = this.pageList(requestDTO);
            List<PoliceNewProfile> records = page.getRecords();
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();

            List<JSONObject> result = new ArrayList<>();
            records.forEach(r -> {
                JSONObject object = new JSONObject();
                String organizationId = r.getOrganizationId();

                object.put("organizationName", SsoCacheUtil.getOrganizationName(organizationId));
                object.put("unitLeader", SsoCacheUtil.getUserNameByIdCard(r.getUnitLeader()));
                object.put("roleModel", SsoCacheUtil.getUserNameByIdCard(r.getRoleModel()));
                object.put("name", r.getName());
                result.add(object);
            });

            Configure config = Configure.builder()
                    .bind("polices", policy)
                    .bind("labors", policy)
                    .useSpringEL()
                    .build();

            Map<String, Object> data = new HashMap<>();
            data.put("polices", result);


            XWPFTemplate template = XWPFTemplate.compile("conf/template/new_police_list.docx", config)
                    .render(data);
            response.setContentType("application/octet-stream");
            String fileName = URLEncoder.encode("新警画像", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".docx");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            template.write(bos);
            bos.flush();
            out.flush();
            PoitlIOUtils.closeQuietlyMulti(template, bos, out);
        } catch (IOException e) {
            log.error("导出Word失败", e);
        }
    }


    //    @EventListener(ApplicationReadyEvent.class)
    public void cleanOrganization() {
        this.list().forEach(r -> {
            String userOrgIdByIdCard = SsoCacheUtil.getUserOrgIdByIdCard(r.getIdCard());
            r.setUnitLeader(userOrgIdByIdCard);
            this.updateById(r);
        });
    }

    /**
     * 获取新警事件时间轴
     */
    public List<SimpleTimelineEventDTO> getTimelineEvents(SimpleTimelineQueryDTO queryDTO) {
        List<SimpleTimelineEventDTO> events = new ArrayList<>();

        // 获取社团活动事件
//        List<SimpleTimelineEventDTO> clubEvents = getClubActivityEvents(queryDTO.getIdCard());
//        events.addAll(clubEvents);

        List<SimpleTimelineEventDTO> resumeEvents = getResumeEvents(queryDTO.getIdCard());
        events.addAll(resumeEvents);

        List<SimpleTimelineEventDTO> profileEvents = getProfileEvents(queryDTO.getIdCard());
        events.addAll(profileEvents);
        // TODO: 这里可以扩展其他事件类型
        // events.addAll(getTrainingEvents(queryDTO.getIdCard()));
        // events.addAll(getExamEvents(queryDTO.getIdCard()));
        // events.addAll(getAwardEvents(queryDTO.getIdCard()));

        // 按时间倒序排列
        events.sort((e1, e2) -> e2.getEventStartTime().compareTo(e1.getEventStartTime()));

        return events;
    }

    private List<SimpleTimelineEventDTO> getProfileEvents(String idCard) {
        List<SimpleTimelineEventDTO> events = new ArrayList<>();
        PoliceNewProfile one = this.lambdaQuery()
                .eq(PoliceNewProfile::getIdCard, idCard)
                .last("limit 1")
                .one();
        JSONObject evaluation = one.getEvaluation();
        events.addAll(parseProfileObj(evaluation,PoliceEventTypeEnum.EVALUATION));

        JSONObject workRecord = one.getWorkRecord();
        events.addAll(parseProfileObj(workRecord,PoliceEventTypeEnum.WORK_RECORD));
        return events;

    }

    private List<SimpleTimelineEventDTO> parseProfileObj(JSONObject object,PoliceEventTypeEnum eventTypeEnum){
        if (object == null){
            return new ArrayList<>();
        }
        List<SimpleTimelineEventDTO> events = new ArrayList<>();
        if (object.containsKey("one")){
            String content = object.getByPath("one.content").toString();
            String updateTime = object.getByPath("one.updateTime").toString();
            SimpleTimelineEventDTO event = SimpleTimelineEventDTO.builder()
                    .eventTypeEnum(eventTypeEnum)
                    .eventType(eventTypeEnum.getName())
                    .title("一季度")
                    .description(content)
                    .eventStartTime(DateUtil.parse(updateTime).toLocalDateTime())
                    .build();
            events.add(event);
        }
        if (object.containsKey("two")){
            String content = object.getByPath("two.content").toString();
            String updateTime = object.getByPath("two.updateTime").toString();
            SimpleTimelineEventDTO event = SimpleTimelineEventDTO.builder()
                    .eventTypeEnum(eventTypeEnum)
                    .eventType(eventTypeEnum.getName())
                    .title("二季度")
                    .description(content)
                    .eventStartTime(DateUtil.parse(updateTime).toLocalDateTime())
                    .build();
            events.add(event);
        }
        if (object.containsKey("three")){
            String content = object.getByPath("three.content").toString();
            String updateTime = object.getByPath("three.updateTime").toString();
            SimpleTimelineEventDTO event = SimpleTimelineEventDTO.builder()
                    .eventTypeEnum(eventTypeEnum)
                    .eventType(eventTypeEnum.getName())
                    .title("三季度")
                    .description(content)
                    .eventStartTime(DateUtil.parse(updateTime).toLocalDateTime())
                    .build();
            events.add(event);

        }
        if (object.containsKey("four")){
            String content = object.getByPath("four.content").toString();
            String updateTime = object.getByPath("four.updateTime").toString();
            SimpleTimelineEventDTO event = SimpleTimelineEventDTO.builder()
                    .eventTypeEnum(eventTypeEnum)
                    .eventType(eventTypeEnum.getName())
                    .title("四季度")
                    .description(content)
                    .eventStartTime(DateUtil.parse(updateTime).toLocalDateTime())
                    .build();
            events.add(event);
        }
        return events;
    }


    private List<SimpleTimelineEventDTO> getResumeEvents(String idCard) {
        List<SimpleTimelineEventDTO> events = new ArrayList<>();
        List<PoliceResume> list = policeResumeService.list(Wrappers.<PoliceResume>lambdaQuery()
                .eq(PoliceResume::getIdCard, idCard));
        for (PoliceResume policeResume : list) {
            SimpleTimelineEventDTO event = SimpleTimelineEventDTO.builder()
                    .eventTypeEnum(PoliceEventTypeEnum.POLICE_RESUME)
                    .eventType(PoliceEventTypeEnum.POLICE_RESUME.getName())
                    .title("工作单位:"+policeResume.getWorkUnit())
                    .eventStartTime(policeResume.getStartDate())
                    .eventEndTime(policeResume.getEndDate())
                    .build();
            events.add(event);
        }
        return events;
    }


    /**
     * 获取社团活动事件
     */
    private List<SimpleTimelineEventDTO> getClubActivityEvents(String idCard) {
        List<SimpleTimelineEventDTO> events = new ArrayList<>();

        try {
            // 查询该人员参与的社团活动
            List<PoliceClubActivity> activities = policeClubActivityService.list(
                    Wrappers.<PoliceClubActivity>lambdaQuery()
                            .apply("JSON_CONTAINS(participants, JSON_QUOTE({0}))", idCard)
                            .orderByDesc(PoliceClubActivity::getActivityTime)
            );

            for (PoliceClubActivity activity : activities) {
                PoliceEventTypeEnum eventTypeEnum = PoliceEventTypeEnum.CLUB_ACTIVITY;
                SimpleTimelineEventDTO event = SimpleTimelineEventDTO.builder()
                        .eventId(String.valueOf(activity.getId()))
                        .eventTypeEnum(eventTypeEnum)
                        .eventType(eventTypeEnum.getName())
                        .title(StrUtil.isNotBlank(activity.getActivityName()) ? activity.getActivityName() : "社团活动")
                        .description("参与社团活动")
                        .eventStartTime(activity.getActivityTime())
                        .location(activity.getLocation())
                        .build();
                events.add(event);
            }
        } catch (Exception e) {
            log.error("获取社团活动事件失败", e);
        }

        return events;
    }

    /**
     * 获取新警排名统计
     * @param requestDTO 请求参数
     * @return 排名结果
     */
    public Page<NewPoliceRankingDTO> getNewPoliceRanking(NewPoliceRankingRequestDTO requestDTO) {
        String organizationId = requestDTO.getOrganizationId();

        // 处理组织机构ID逻辑，与其他查询保持一致
        if (StrUtil.isNotBlank(organizationId)) {
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(organizationId)) {
                // 非320412000000时，截取前8位
                organizationId = organizationId.substring(0, 8);
            } else {
                organizationId = null;
            }
        }

        // 根据排名类型获取数量数据
        NewPoliceRankingTypeEnum rankingType = NewPoliceRankingTypeEnum.getByCode(requestDTO.getRankingType());
        if (rankingType == null) {
            throw new IllegalArgumentException("无效的排名类型: " + requestDTO.getRankingType());
        }

        Map<String, Long> countMap;
        List<PoliceNewProfile> newPoliceList = getPoliceNewProfiles(organizationId);
        switch (rankingType) {
            case POLICE_CALL:
                countMap = getNewPoliceCallCountMap(newPoliceList, requestDTO.getStartTime(), requestDTO.getEndTime());
                break;
            case CASE_HANDLING:
                countMap = getNewPoliceCaseCountMap(newPoliceList, requestDTO.getStartTime(), requestDTO.getEndTime());
                break;
            default:
                throw new IllegalArgumentException("不支持的排名类型: " + requestDTO.getRankingType());
        }

        // 获取新警基本信息并合并数量数据
        List<NewPoliceRankingDTO> allRankings = buildRankingList(newPoliceList, countMap, requestDTO.getRankingType());

        // 按数量降序排序
        allRankings.sort((a, b) -> {
            int countCompare = Long.compare(b.getCount(), a.getCount());
            if (countCompare != 0) {
                return countCompare;
            }
            // 数量相同时按姓名排序
            return a.getIdCard().compareTo(b.getIdCard());
        });

        // 设置排名
        for (int i = 0; i < allRankings.size(); i++) {
            allRankings.get(i).setRanking(i + 1);
        }

        // 分页处理
        int total = allRankings.size();
        int start = (requestDTO.getPage() - 1) * requestDTO.getLimit();
        int end = Math.min(start + requestDTO.getLimit(), total);

        List<NewPoliceRankingDTO> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = allRankings.subList(start, end);
        }

        Page<NewPoliceRankingDTO> resultPage = new Page<>(requestDTO.getPage(), requestDTO.getLimit(), total);
        resultPage.setRecords(pageRecords);

        return resultPage;
    }

    /**
     * 获取新警接处警数量Map（需要您自己实现具体的查询逻辑）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 身份证号->数量的Map
     */
    public Map<String, Long> getNewPoliceCallCountMap(List<PoliceNewProfile> newProfilesList, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 请在这里实现具体的接处警数量查询逻辑
        // 返回格式：Map<身份证号, 接处警数量>
        // 示例实现：
        Map<String, Long> countMap = new HashMap<>();

        // 您的查询逻辑应该返回类似这样的数据：

        return countMap;
    }

    /**
     * 获取新警办案数量Map（需要您自己实现具体的查询逻辑）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 身份证号->数量的Map
     */
    public Map<String, Long> getNewPoliceCaseCountMap(List<PoliceNewProfile> newProfilesList, LocalDateTime startTime, LocalDateTime endTime) {
        // TODO: 请在这里实现具体的办案数量查询逻辑
        // 返回格式：Map<身份证号, 办案数量>
        // 示例实现：
        Map<String, Long> countMap = new HashMap<>();
//        LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
//
//
//        queryWrapper.and(w->{
//            for (PoliceNewProfile policeNewProfile : newProfilesList) {
//                w.like(ViewCaseInfoTaskDocument::getWorkPerson,policeNewProfile.getPoliceNumber());
//            }
//        });
//
//        List<ViewCaseInfoTaskDocument> viewCaseInfoTaskDocuments = viewCaseInfoTaskDocumentMapper.selectList(queryWrapper);
//        Map<String,Long> count = new HashMap<>();
//        for (ViewCaseInfoTaskDocument viewCaseInfoTaskDocument : viewCaseInfoTaskDocuments) {
//
//
//        }

        return countMap;
    }

    /**
     * 构建排名列表
     * @param countMap 数量Map
     * @param rankingType 排名类型
     * @return 排名列表
     */
    private List<NewPoliceRankingDTO> buildRankingList(List<PoliceNewProfile> newPoliceList, Map<String, Long> countMap, String rankingType) {

        // 构建排名结果
        List<NewPoliceRankingDTO> rankingList = new ArrayList<>();
        for (PoliceNewProfile police : newPoliceList) {
            NewPoliceRankingDTO ranking = new NewPoliceRankingDTO();
            ranking.setIdCard(police.getIdCard());
            ranking.setRankingType(rankingType);
            // 从countMap中获取对应的数量，如果没有则为0
            Long count = countMap.getOrDefault(police.getIdCard(), 0L);
            ranking.setCount(count);
            rankingList.add(ranking);
        }

        return rankingList;
    }

    private List<PoliceNewProfile> getPoliceNewProfiles(String organizationId) {
        // 查询新警基本信息
        LambdaQueryWrapper<PoliceNewProfile> queryWrapper = Wrappers.<PoliceNewProfile>lambdaQuery()
                .eq(PoliceNewProfile::getIsDeleted, 0);

        if (StrUtil.isNotBlank(organizationId)) {
            queryWrapper.likeRight(PoliceNewProfile::getOrganizationId, organizationId);
        }

        List<PoliceNewProfile> newPoliceList = this.list(queryWrapper);
        return newPoliceList;
    }

}
