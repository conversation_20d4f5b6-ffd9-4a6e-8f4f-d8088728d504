package com.hl.archive.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.archive.domain.dto.PoliceWorkQualityCountReturnDTO;
import com.hl.archive.domain.dto.WorkQualityCountQueryDTO;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.search.document.*;
import com.hl.archive.search.mapper.*;
import com.hl.archive.utils.MeasureUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.common.params.SFunction;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceWorkQualityCountService {


    private final ViewCaseInfoTaskDocumentMapper viewCaseInfoTaskDocumentMapper;

    private final ViewCaseMeasureStatDocumentMapper viewCaseMeasureStatDocumentMapper;

    private final ViewCaseExamineTaskDocumentMapper viewCaseExamineTaskDocumentMapper;

    private final ViewPoliceExamineTaskDocumentMapper viewPoliceExamineTaskDocumentMapper;

    private final ViewCasePlaceExamineTaskDocumentMapper viewCasePlaceExamineTaskDocumentMapper;

    private final ViewTaskTimeoutResultDocumentMapper viewTaskTimeoutResultDocumentMapper;


    private final ViewPoliceExportDocumentMapper viewPoliceExportDocumentMapper;

    private final PoliceBasicInfoService policeBasicInfoService;

    private final ViewPoliceNotifyDocumentMapper viewPoliceNotifyDocumentMapper;

    private <T> void applyDateRange(LambdaEsQueryWrapper<T> wrapper,
                                SFunction<T, ?> column,
                                String startDate,
                                String endDate) {
        if (StrUtil.isNotBlank(startDate)) {
            wrapper.ge(column,startDate,ZoneId.systemDefault(),DatePattern.NORM_DATETIME_PATTERN);
        }
        if (StrUtil.isNotBlank(endDate)){
            wrapper.le(column,endDate,ZoneId.systemDefault(),DatePattern.NORM_DATETIME_PATTERN);
        }
    }

    /**
     * 构建案件措施统计查询条件 - 支持主办人和协办人查询
     */
    private LambdaEsQueryWrapper<ViewCaseMeasureStatDocument> buildCaseMeasureQuery(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseMeasureStatDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> w
                    .like(ViewCaseMeasureStatDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewCaseMeasureStatDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
//        TODO: 添加时间查询
        return queryWrapper;
    }

    /**
     * 构建案件考评查询条件
     */
    private LambdaEsQueryWrapper<ViewCaseExamineTaskDocument> buildCaseExamineQuery(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseExamineTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> w
                    .like(ViewCaseExamineTaskDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewCaseExamineTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
        // 根据考评日期查询筛选
        applyDateRange(queryWrapper,ViewCaseExamineTaskDocument::getExamineDate, queryDTO.getStartDate(), queryDTO.getEndDate());
        return queryWrapper;
    }

    /**
     * 构建警情考评查询条件
     */
    private LambdaEsQueryWrapper<ViewPoliceExamineTaskDocument> buildPoliceExamineQuery(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewPoliceExamineTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> w
                    .like(ViewPoliceExamineTaskDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewPoliceExamineTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
        applyDateRange(queryWrapper,ViewPoliceExamineTaskDocument::getExamineTime, queryDTO.getStartDate(), queryDTO.getEndDate());
        return queryWrapper;
    }

    /**
     * 构建办案场所考评查询条件
     */
    private LambdaEsQueryWrapper<ViewCasePlaceExamineTaskDocument> buildCasePlaceExamineQuery(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCasePlaceExamineTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> w
                    .like(ViewCasePlaceExamineTaskDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewCasePlaceExamineTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
        applyDateRange(queryWrapper,ViewCasePlaceExamineTaskDocument::getExamineDate, queryDTO.getStartDate(), queryDTO.getEndDate());
        return queryWrapper;
    }

    /**
     * 构建超时结果查询条件
     */
    private LambdaEsQueryWrapper<ViewTaskTimeoutResultDocument> buildTimeoutQuery(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewTaskTimeoutResultDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> w
                    .like(ViewTaskTimeoutResultDocument::getWorkPerson, queryDTO.getPoliceNumber())
                    .or()
                    .like(ViewTaskTimeoutResultDocument::getCoworkPerson, queryDTO.getPoliceNumber()));
        }
        return queryWrapper;
    }

    /**
     * 构建案件信息查询条件 - 主办案件专用
     */
    private LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> buildZBAJQuery(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.like(ViewCaseInfoTaskDocument::getWorkPerson, queryDTO.getPoliceNumber());
        }
        // 根据案件受理时间来查询
        applyDateRange(queryWrapper,ViewCaseInfoTaskDocument::getAcceptTime, queryDTO.getStartDate(), queryDTO.getEndDate());

        return queryWrapper;
    }

    /**
     * 构建案件信息查询条件 - 协办案件专用
     */
    private LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> buildXBAJQuery(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.like(ViewCaseInfoTaskDocument::getCoworkPerson, queryDTO.getPoliceNumber());
        }
        // 受案时间查询
        applyDateRange(queryWrapper,ViewCaseInfoTaskDocument::getAcceptTime, queryDTO.getStartDate(), queryDTO.getEndDate());
        return queryWrapper;
    }

    /**
     * 主办案件数量
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long countZBAJ(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> queryWrapper = buildZBAJQuery(queryDTO);
        return viewCaseInfoTaskDocumentMapper.selectCount(queryWrapper);
    }

    /**
     * 协办案件数量
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long countXBAJ(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseInfoTaskDocument> queryWrapper = buildXBAJQuery(queryDTO);
        return viewCaseInfoTaskDocumentMapper.selectCount(queryWrapper);
    }

    /**
     * 案件措施统计 - 使用并发处理和线程安全计数器优化性能
     *
     * @param queryDTO 查询参数
     * @return 统计结果
     */
    public PoliceWorkQualityCountReturnDTO countAJCS(WorkQualityCountQueryDTO queryDTO) {
        PoliceWorkQualityCountReturnDTO qualityCountReturnDTO = new PoliceWorkQualityCountReturnDTO();

        // 使用线程安全的计数器
        PoliceWorkQualityCountReturnDTO.ZaCaseStat zaCaseStat = new PoliceWorkQualityCountReturnDTO.ZaCaseStat();
        PoliceWorkQualityCountReturnDTO.XsCaseStat xsCaseStat = new PoliceWorkQualityCountReturnDTO.XsCaseStat();
        qualityCountReturnDTO.setZaCaseStat(zaCaseStat);
        qualityCountReturnDTO.setXsCaseStat(xsCaseStat);

        // 构建查询条件
        LambdaEsQueryWrapper<ViewCaseMeasureStatDocument> queryWrapper = buildCaseMeasureQuery(queryDTO);

        List<ViewCaseMeasureStatDocument> viewCaseMeasureStatDocuments = viewCaseMeasureStatDocumentMapper.selectList(queryWrapper);
        if (viewCaseMeasureStatDocuments.isEmpty()) {
            return qualityCountReturnDTO;
        }

        // 使用并行流进行分组，提升性能
        Map<Integer, List<ViewCaseMeasureStatDocument>> crimeTypeMap = viewCaseMeasureStatDocuments.parallelStream()
                .collect(Collectors.groupingBy(ViewCaseMeasureStatDocument::getIsCrime));

        // 并行处理治安案件统计
        if (crimeTypeMap.containsKey(1)) {
            processZaCase(crimeTypeMap.get(1), zaCaseStat);
        }

        // 并行处理刑事案件统计
        if (crimeTypeMap.containsKey(2)) {
            processXsCase(crimeTypeMap.get(2), xsCaseStat);
        }

        return qualityCountReturnDTO;
    }

    /**
     * 处理治安案件统计 - 使用线程安全的LongAdder进行并发计数
     */
    private void processZaCase(List<ViewCaseMeasureStatDocument> zaCaseList, PoliceWorkQualityCountReturnDTO.ZaCaseStat zaCaseStat) {
        // 使用LongAdder提供更好的并发性能
        LongAdder xzjlCounter = new LongAdder();
        LongAdder chCounter = new LongAdder();
        LongAdder jgCounter = new LongAdder();
        LongAdder fkCounter = new LongAdder();
        LongAdder bycfCounter = new LongAdder();

        // 并行处理提升性能
        zaCaseList.parallelStream()
                .filter(m -> StrUtil.isNotBlank(m.getResultCode()))
                .forEach(m -> {
                    String resultCode = m.getResultCode();
                    if (MeasureUtils.XING_ZHENG_JU_LIU.contains(resultCode)) {
                        xzjlCounter.increment();
                    }
                    if (MeasureUtils.XING_ZHENG_CHUAN_HUAN.contains(resultCode)) {
                        chCounter.increment();
                    }
                    if (MeasureUtils.JING_GAO.contains(resultCode)) {
                        jgCounter.increment();
                    }
                    if (MeasureUtils.FA_KUAN.contains(resultCode)) {
                        fkCounter.increment();
                    }
                    if (MeasureUtils.BU_YU_CHU_FA.contains(resultCode)) {
                        bycfCounter.increment();
                    }
                });

        // 设置统计结果
        zaCaseStat.setXzjlCount(xzjlCounter.longValue());
        zaCaseStat.setChCount(chCounter.longValue());
        zaCaseStat.setJgCount(jgCounter.longValue());
        zaCaseStat.setFkCount(fkCounter.longValue());
        zaCaseStat.setBycfCount(bycfCounter.longValue());
    }

    /**
     * 处理刑事案件统计 - 使用线程安全的LongAdder进行并发计数
     */
    private void processXsCase(List<ViewCaseMeasureStatDocument> xsCaseList, PoliceWorkQualityCountReturnDTO.XsCaseStat xsCaseStat) {
        // 使用LongAdder提供更好的并发性能
        LongAdder xschCounter = new LongAdder();
        LongAdder jsjzCounter = new LongAdder();
        LongAdder xsjlCounter = new LongAdder();
        LongAdder qbhsCounter = new LongAdder();
        LongAdder dbCounter = new LongAdder();
        LongAdder yszsCounter = new LongAdder();
        LongAdder tqqsCounter = new LongAdder();

        // 并行处理提升性能
        xsCaseList.parallelStream()
                .filter(m -> StrUtil.isNotBlank(m.getResultCode()))
                .forEach(m -> {
                    String resultCode = m.getResultCode();
                    if (MeasureUtils.XING_SHI_CHUAN_HUAN.contains(resultCode)) {
                        xschCounter.increment();
                    }
                    if (MeasureUtils.JIAN_SHI_JU_ZHU.contains(resultCode)) {
                        jsjzCounter.increment();
                    }
                    if (MeasureUtils.XING_SHI_JU_LIU.contains(resultCode)) {
                        xsjlCounter.increment();
                    }
                    if (MeasureUtils.QU_BAO_HOU_SHEN.contains(resultCode)) {
                        qbhsCounter.increment();
                    }
                    if (MeasureUtils.DAI_BU.contains(resultCode)) {
                        dbCounter.increment();
                    }
                    if (MeasureUtils.YI_SONG_ZHI_SU.contains(resultCode)) {
                        yszsCounter.increment();
                    }
                    if (MeasureUtils.TI_QING_QI_SU.contains(resultCode)) {
                        tqqsCounter.increment();
                    }
                });

        // 设置统计结果
        xsCaseStat.setXschCount(xschCounter.longValue());
        xsCaseStat.setJsjzCount(jsjzCounter.longValue());
        xsCaseStat.setXsjlCount(xsjlCounter.longValue()); // 修复原代码bug：这里应该设置xsjlCount而不是jsjzCount
        xsCaseStat.setQbhsCount(qbhsCounter.longValue());
        xsCaseStat.setDbCount(dbCounter.longValue());
        xsCaseStat.setYszsCount(yszsCounter.longValue());
        xsCaseStat.setTqqsCount(tqqsCounter.longValue());
    }


    /**
     * 案件考评数量统计
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long caseExamineCount(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCaseExamineTaskDocument> queryWrapper = buildCaseExamineQuery(queryDTO);
        return viewCaseExamineTaskDocumentMapper.selectCount(queryWrapper);
    }

    /**
     * 警情考评数量
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long policeExamineCount(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewPoliceExamineTaskDocument> queryWrapper = buildPoliceExamineQuery(queryDTO);
        return viewPoliceExamineTaskDocumentMapper.selectCount(queryWrapper);
    }

    /**
     * 办案场所考评
     *
     * @param queryDTO 查询参数
     * @return 数量
     */
    public Long casePlaceExamineCounr(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewCasePlaceExamineTaskDocument> queryWrapper = buildCasePlaceExamineQuery(queryDTO);
        return viewCasePlaceExamineTaskDocumentMapper.selectCount(queryWrapper);
    }

    /**
     * 超时统计 - 使用线程安全的LongAdder优化并发性能
     *
     * @param queryDTO 查询参数
     * @return 超时统计结果
     */
    public PoliceWorkQualityCountReturnDTO timeoutCount(WorkQualityCountQueryDTO queryDTO) {
        // 使用LongAdder替代AtomicReference，提供更好的并发性能
        LongAdder signTimeoutCounter = new LongAdder();
        LongAdder workTimeoutCounter = new LongAdder();

        LambdaEsQueryWrapper<ViewTaskTimeoutResultDocument> queryWrapper = buildTimeoutQuery(queryDTO);

        List<ViewTaskTimeoutResultDocument> timeoutDocuments = viewTaskTimeoutResultDocumentMapper.selectList(queryWrapper);

        // 使用并行流处理，提升性能
        timeoutDocuments.parallelStream()
                .filter(doc -> StrUtil.isNotBlank(doc.getTimeoutType()))
                .forEach(doc -> {
                    String timeoutType = doc.getTimeoutType();
                    if (timeoutType.contains("sign")) {
                        signTimeoutCounter.increment();
                    }
                    if (timeoutType.contains("work")) {
                        workTimeoutCounter.increment();
                    }
                });

        PoliceWorkQualityCountReturnDTO qualityCountReturnDTO = new PoliceWorkQualityCountReturnDTO();
        qualityCountReturnDTO.setSignTimeoutCount(signTimeoutCounter.longValue());
        qualityCountReturnDTO.setWorkTimeoutCount(workTimeoutCounter.longValue());
        return qualityCountReturnDTO;
    }


    public Long jqCount(WorkQualityCountQueryDTO queryDTO) {
        String policeNumber = queryDTO.getPoliceNumber();
        PoliceBasicInfo basicInfo = policeBasicInfoService.getOne(Wrappers.<PoliceBasicInfo>lambdaQuery()
                .eq(PoliceBasicInfo::getPoliceNumber, policeNumber)
                .last(" limit 1"));
        List<String> jzCode = basicInfo.getJzCode();
        if (jzCode == null || jzCode.isEmpty()) {
            return 0L;
        }
        LambdaEsQueryWrapper<ViewPoliceExportDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.and(w -> {
            for (String s : jzCode) {
                w.like(ViewPoliceExportDocument::getCjr, s)
                        .or();
            }
        });
        if (StrUtil.isNotBlank(queryDTO.getStartDate())){
            String format = DateUtil.format(DateUtil.parse(queryDTO.getStartDate()), "yyyyMMddHHmmss");
            queryWrapper.ge(ViewPoliceExportDocument::getBjdhsjTime,format);
        }
        if (StrUtil.isNotBlank(queryDTO.getEndDate())){
            String format = DateUtil.format(DateUtil.parse(queryDTO.getEndDate()), "yyyyMMdd");
            queryWrapper.le(ViewPoliceExportDocument::getCjr,format);
        }
        return viewPoliceExportDocumentMapper.selectCount(queryWrapper);
    }

    public EsPageInfo<ViewPoliceExportDocument> pageJq(WorkQualityCountQueryDTO queryDTO) {
        String policeNumber = queryDTO.getPoliceNumber();
        PoliceBasicInfo basicInfo = policeBasicInfoService.getOne(Wrappers.<PoliceBasicInfo>lambdaQuery()
                .eq(PoliceBasicInfo::getPoliceNumber, policeNumber)
                .last(" limit 1"));
        List<String> jzCode = basicInfo.getJzCode();
        if (jzCode == null || jzCode.isEmpty()) {
            return new EsPageInfo<>();
        }
        LambdaEsQueryWrapper<ViewPoliceExportDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.and(w -> {
            for (String s : jzCode) {
                w.like(ViewPoliceExportDocument::getCjr, s)
                        .or();
            }
        });
        EsPageInfo<ViewPoliceExportDocument> viewPoliceExportDocumentEsPageInfo = viewPoliceExportDocumentMapper.pageQuery(queryWrapper, queryDTO.getPage(), queryDTO.getLimit());
        return viewPoliceExportDocumentEsPageInfo;
    }


    public Long nwtbCount(WorkQualityCountQueryDTO queryDTO) {
        LambdaEsQueryWrapper<ViewPoliceNotifyDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotBlank(queryDTO.getPoliceNumber())) {
            queryWrapper.and(w -> {
                w.like(ViewPoliceNotifyDocument::getNotifyPerson, queryDTO.getPoliceNumber());
            });
        }
        applyDateRange(queryWrapper,ViewPoliceNotifyDocument::getNotifyTime,queryDTO.getStartDate(),queryDTO.getEndDate());

        Long l = viewPoliceNotifyDocumentMapper.selectCount(queryWrapper);
        return l;
    }


}
