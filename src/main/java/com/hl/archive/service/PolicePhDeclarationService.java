package com.hl.archive.service;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.hl.archive.utils.SsoCacheUtil;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PolicePhDeclarationMapper;
import com.hl.archive.domain.entity.PolicePhDeclaration;
@Service
public class  PolicePhDeclarationService extends ServiceImpl<PolicePhDeclarationMapper, PolicePhDeclaration> {


//    @EventListener(ApplicationReadyEvent.class)
    public void importData(){

        ExcelReader reader = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\健康报告-个人填报-民警.xls");
        List<Map<String, Object>> mapList = reader.readAll();
        for (Map<String, Object> data : mapList) {
            String policeNumber = data.get("警号").toString();
            String diseaseName = data.get("疾病").toString();
            String[] split = diseaseName.split("[，；、]");
            for (String s : split) {
                PolicePhDeclaration policePhDeclaration = new PolicePhDeclaration();
                policePhDeclaration.setIdCard(SsoCacheUtil.getUserIdCardByPoliceId(policeNumber));
                policePhDeclaration.setDiseaseName(s);
                this.save(policePhDeclaration);
            }
        }
        reader.close();
    }
}
