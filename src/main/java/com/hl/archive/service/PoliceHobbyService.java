package com.hl.archive.service;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.dict.entity.DictData;
import com.hl.dict.service.DictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceHobbyMapper;
import com.hl.archive.domain.entity.PoliceHobby;
@Service
@RequiredArgsConstructor
public class PoliceHobbyService extends ServiceImpl<PoliceHobbyMapper, PoliceHobby> {

    private final DictDataService dicService;

//    @EventListener(ApplicationReadyEvent.class)
    public void importPoliceHobby(){
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/爱好特长-民警.xls");


        List<Map<String, Object>> mapList = reader.readAll();
        for (Map<String, Object> map : mapList) {
            String policeNumber = map.get("警号").toString();
            String hobby = map.get("爱好").toString();
            String[] split = hobby.split("，");
            for (String s : split) {
                DictData hobby1 = dicService.lambdaQuery()
                        .eq(DictData::getDictType, "hobby")
                        .eq(DictData::getDictName, s)
                        .last(" limit 1")
                        .one();
                if (hobby1 != null) {
                    String dictName = hobby1.getDictName();
                    Long id = hobby1.getId();
                    String dictType = hobby1.getDictType();
                    PoliceHobby policeHobby = new PoliceHobby();
                    policeHobby.setOrganizationId(SsoCacheUtil.getUserOrgIdByPoliceId(policeNumber));
                    policeHobby.setIdCard(SsoCacheUtil.getUserIdCardByPoliceId(policeNumber));
                    policeHobby.setHobbyName(dictName);
                    policeHobby.setHobbyCode(id.toString());
                    policeHobby.setHobbyType(dictType);
                    this.save(policeHobby);
                }
            }

        }
    }
}
