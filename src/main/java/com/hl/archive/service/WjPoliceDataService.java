package com.hl.archive.service;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class WjPoliceDataService {

    @Resource
    @Qualifier("datasource3DataSource")
    private DataSource wjDataSource;

    private final TimedCache<String, List<JSONObject>> GROUP_INFO_CACHE = CacheUtil.newTimedCache(1000 * 60 * 10);

    public void refreshPoliceGroupInfo() {

        try {
            List<Entity> query = DbUtil.use(wjDataSource)
                    .query("select * from police_group");
            GROUP_INFO_CACHE.clear();

            query.forEach(entity -> {
                JSONObject data = new JSONObject();
                JSONObject result = entity.toBean(data, false);
                String groupLeader = result.getString("group_leader");
                if (StrUtil.isNotBlank(groupLeader)) {
                    if (GROUP_INFO_CACHE.containsKey(groupLeader)) {
                        GROUP_INFO_CACHE.get(groupLeader).add(result);
                    } else {
                        List<JSONObject> list = new ArrayList<>();
                        list.add(result);
                        GROUP_INFO_CACHE.put(groupLeader, list);
                    }
                }

                String groupPerson = result.getString("group_person");
                if (StrUtil.isBlank(groupPerson)) {
                    return;
                }
                String[] split = groupPerson.split(",");
                Arrays.stream(split).forEach(person -> {
                    if (StrUtil.isBlankIfStr(person)){
                        return;
                    }
                    if (GROUP_INFO_CACHE.containsKey(person)) {
                        GROUP_INFO_CACHE.get(person).add(result);
                    } else {
                        List<JSONObject> list = new ArrayList<>();
                        list.add(result);
                        GROUP_INFO_CACHE.put(person, list);
                    }
                });
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            log.info("共缓存{}个民警分组信息", GROUP_INFO_CACHE.size());
        }
    }

    public List<JSONObject> getGroupInfo(String policeNumber) {
        if (GROUP_INFO_CACHE.containsKey(policeNumber)) {
            return GROUP_INFO_CACHE.get(policeNumber);
        } else {
            refreshPoliceGroupInfo();
        }
        if (GROUP_INFO_CACHE.containsKey(policeNumber)) {
            return GROUP_INFO_CACHE.get(policeNumber);
        } else {
            return new ArrayList<>();
        }
    }


}
