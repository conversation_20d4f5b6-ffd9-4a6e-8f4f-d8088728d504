package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.ViolationPersonQueryDTO;
import com.hl.archive.domain.dto.ViolationPersonVO;
import com.hl.archive.domain.entity.PoliceViolationPerson;
import com.hl.archive.domain.entity.PoliceViolationResult;
import com.hl.archive.domain.entity.PoliceViolationSummary;
import com.hl.archive.mapper.PoliceViolationPersonMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PoliceViolationPersonService extends ServiceImpl<PoliceViolationPersonMapper, PoliceViolationPerson> {

    private final PoliceViolationSummaryService policeViolationSummaryService;
    private final PoliceViolationResultService policeViolationResultService;

    /**
     * 分页查询违法违纪人员综合信息
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    public Page<ViolationPersonVO> pageViolationPersons(ViolationPersonQueryDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<PoliceViolationPerson> wrapper = buildQueryWrapper(dto);

        // 分页查询人员信息
        Page<PoliceViolationPerson> personPage = this.page(Page.of(dto.getPage(), dto.getLimit()), wrapper);

        if (personPage.getRecords().isEmpty()) {
            return new Page<>(dto.getPage(), dto.getLimit(), 0);
        }

        // 获取人员的xxzjbh和wtXxzjbh列表
        List<String> xxzjbhList = personPage.getRecords().stream()
                .map(PoliceViolationPerson::getXxzjbh)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        List<String> wtXxzjbhList = personPage.getRecords().stream()
                .map(PoliceViolationPerson::getWtXxzjbh)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        // 批量查询问题汇总信息
        Map<String, PoliceViolationSummary> summaryMap = new HashMap<>();
        if (!wtXxzjbhList.isEmpty()) {
            List<PoliceViolationSummary> summaries = policeViolationSummaryService.list(
                    Wrappers.<PoliceViolationSummary>lambdaQuery()
                            .in(PoliceViolationSummary::getXxzjbh, wtXxzjbhList)
            );
            summaryMap = summaries.stream()
                    .collect(Collectors.toMap(PoliceViolationSummary::getXxzjbh, s -> s, (v1, v2) -> v1));
        }

        // 批量查询处理结果信息
        Map<String, List<PoliceViolationResult>> resultMap = new HashMap<>();
        if (!xxzjbhList.isEmpty()) {
            List<PoliceViolationResult> results = policeViolationResultService.list(
                    Wrappers.<PoliceViolationResult>lambdaQuery()
                            .in(PoliceViolationResult::getRyXxzjbh, xxzjbhList)
                            .orderByDesc(PoliceViolationResult::getClsj)
            );
            resultMap = results.stream()
                    .collect(Collectors.groupingBy(PoliceViolationResult::getRyXxzjbh));
        }

        // 构建返回结果
        final Map<String, PoliceViolationSummary> finalSummaryMap = summaryMap;
        final Map<String, List<PoliceViolationResult>> finalResultMap = resultMap;

        List<ViolationPersonVO> voList = personPage.getRecords().stream()
                .map(person -> {
                    PoliceViolationSummary summary = finalSummaryMap.get(person.getWtXxzjbh());
                    List<PoliceViolationResult> results = finalResultMap.get(person.getXxzjbh());
                    return ViolationPersonVO.fromEntities(person, summary, results);
                })
                .collect(Collectors.toList());

        Page<ViolationPersonVO> resultPage = new Page<>(dto.getPage(), dto.getLimit(), personPage.getTotal());
        resultPage.setRecords(voList);

        return resultPage;
    }

    /**
     * 构建查询条件
     */
    public LambdaQueryWrapper<PoliceViolationPerson> buildQueryWrapper(ViolationPersonQueryDTO dto) {
        LambdaQueryWrapper<PoliceViolationPerson> wrapper = Wrappers.<PoliceViolationPerson>lambdaQuery();

        // 基本条件
        wrapper.like(StrUtil.isNotBlank(dto.getName()), PoliceViolationPerson::getName, dto.getName())
                .eq(StrUtil.isNotBlank(dto.getGender()), PoliceViolationPerson::getGender, dto.getGender())
                .like(StrUtil.isNotBlank(dto.getCaseOrg()), PoliceViolationPerson::getCaseOrg, dto.getCaseOrg())
                .like(StrUtil.isNotBlank(dto.getPoliceDept()), PoliceViolationPerson::getPoliceDept, dto.getPoliceDept())
                .like(StrUtil.isNotBlank(dto.getPosition()), PoliceViolationPerson::getPosition, dto.getPosition())
                .eq(StrUtil.isNotBlank(dto.getRank()), PoliceViolationPerson::getRank, dto.getRank())
                .eq(StrUtil.isNotBlank(dto.getPoliticalStatus()), PoliceViolationPerson::getPoliticalStatus, dto.getPoliticalStatus())
                .eq(StrUtil.isNotBlank(dto.getIsAccountability()), PoliceViolationPerson::getIsAccountability, dto.getIsAccountability())
                .eq(StrUtil.isNotBlank(dto.getDispositionCategory()), PoliceViolationPerson::getDispositionCategory, dto.getDispositionCategory());

        // 日期范围条件
        if (dto.getBirthDateStart() != null) {
            wrapper.ge(PoliceViolationPerson::getBirthDate, dto.getBirthDateStart());
        }
        if (dto.getBirthDateEnd() != null) {
            wrapper.le(PoliceViolationPerson::getBirthDate, dto.getBirthDateEnd());
        }
        if (dto.getJoinPartyDateStart() != null) {
            wrapper.ge(PoliceViolationPerson::getJoinPartyDate, dto.getJoinPartyDateStart());
        }
        if (dto.getJoinPartyDateEnd() != null) {
            wrapper.le(PoliceViolationPerson::getJoinPartyDate, dto.getJoinPartyDateEnd());
        }

        if (StrUtil.isNotBlank(dto.getOrganizationId())){
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(dto.getOrganizationId())) {
                wrapper.likeRight(PoliceViolationPerson::getOrganizationId,dto.getOrganizationId().substring(0,8));
            }else {
                wrapper.likeRight(PoliceViolationPerson::getOrganizationId,dto.getOrganizationId());
            }
        }

        // 关键字搜索
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            wrapper.and(w -> w.like(PoliceViolationPerson::getName, dto.getKeyword())
                    .or().like(PoliceViolationPerson::getCaseOrg, dto.getKeyword())
                    .or().like(PoliceViolationPerson::getPoliceDept, dto.getKeyword())
                    .or().like(PoliceViolationPerson::getPosition, dto.getKeyword()));
        }
        if (StrUtil.isNotBlank(dto.getIdCard())) {
            wrapper.eq(PoliceViolationPerson::getIdCard, dto.getIdCard());
        }

        if (StrUtil.isNotBlank(dto.getUserType())){
            wrapper.eq(PoliceViolationPerson::getUserType, dto.getUserType());
        }

        wrapper.isNotNull(PoliceViolationPerson::getOrganizationId);

        return wrapper;
    }


}
