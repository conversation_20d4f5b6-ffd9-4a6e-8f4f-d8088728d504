package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.ImportResultDTO;
import com.hl.archive.domain.dto.PoliceQualificationRecordRequestDTO;
import com.hl.archive.domain.entity.FileDetail;
import com.hl.archive.domain.entity.PoliceQualificationRecord;
import com.hl.archive.factory.QualificationRecordStrategyFactory;
import com.hl.archive.mapper.PoliceQualificationRecordMapper;
import com.hl.archive.strategy.QualificationRecordStrategy;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.config.exception.HlErrException;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class PoliceQualificationRecordService extends ServiceImpl<PoliceQualificationRecordMapper, PoliceQualificationRecord> {


    private final FileDetailService fileDetailService;


    private final FileStorageService fileStorageService;

    private final QualificationRecordStrategyFactory qualificationRecordStrategyFactory;

    /**
     * 导入Excel数据
     *
     * @param file     Excel文件
     * @param category 资格类目
     * @return 导入结果
     */
    public ImportResultDTO importData(MultipartFile file, String category) {
        ImportResultDTO result = new ImportResultDTO();

        try {
            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());

            // 从第2行开始读取数据（第1行是表头）
            List<List<Object>> rows = reader.read(1);
            result.setTotalCount(rows.size());

            if (rows.isEmpty()) {
                result.addErrorMessage("Excel文件中没有数据");
                return result;
            }

            List<PoliceQualificationRecord> recordsToSave = new ArrayList<>();

            LocalDateTime now = LocalDateTime.now();

            // 逐行处理数据
            for (int i = 0; i < rows.size(); i++) {
                List<Object> row = rows.get(i);
                int rowNum = i + 2; // Excel行号（从1开始，加上表头）

                try {
                    PoliceQualificationRecord record = parseRowData(row, category, rowNum);
                    if (record != null) {
                        // 设置审计字段
                        record.setCreatedBy(UserUtils.getUser().getIdCard());
                        record.setCreatedAt(now);
                        record.setUpdatedBy(UserUtils.getUser().getIdCard());
                        record.setUpdatedAt(now);
                        record.setStatus((byte) 0); // 默认有效
                        recordsToSave.add(record);
                    }
                } catch (Exception e) {
                    result.addErrorMessage(String.format("第%d行数据解析失败: %s", rowNum, e.getMessage()));
                    log.error("解析第{}行数据失败", rowNum, e);
                }
            }

            // 批量保存数据
            if (!recordsToSave.isEmpty()) {
                boolean saveResult = saveBatch(recordsToSave);
                if (saveResult) {
                    result.setSuccessCount(recordsToSave.size());
                    log.info("成功导入{}条记录", recordsToSave.size());
                } else {
                    result.addErrorMessage("批量保存数据失败");
                }
            }

        } catch (IOException e) {
            result.addErrorMessage("读取Excel文件失败: " + e.getMessage());
            log.error("读取Excel文件失败", e);
        } catch (Exception e) {
            result.addErrorMessage("导入过程中发生异常: " + e.getMessage());
            log.error("导入过程中发生异常", e);
        }

        return result;
    }

    /**
     * 解析行数据
     */
    private PoliceQualificationRecord parseRowData(List<Object> row, String category, int rowNum) {
        if (row == null || row.isEmpty()) {
            throw new RuntimeException("行数据为空");
        }

        PoliceQualificationRecord record = new PoliceQualificationRecord();

        try {

            // 单位
            if (row.size() > 0 && row.get(0) != null) {
                String trim = row.get(0).toString().trim();
                String organizationId = SsoCacheUtil.getOrganizationIdByName(trim);
                record.setOrganizationId(organizationId);
            }

            // 姓名（必填）
            if (row.size() > 1 && row.get(1) != null) {
                String trim = row.get(1).toString().trim();

                if (StrUtil.isBlank(trim)) {
                    throw new RuntimeException("姓名不能为空");
                }
                // 根据姓名 和单位 找到 身份证号
                List<JSONObject> idCardByNameAndOrganizationName = SsoCacheUtil.getIdCardByNameAndOrganizationName(trim, record.getOrganizationId());
                if (idCardByNameAndOrganizationName.size() > 1) {
                    throw new RuntimeException("找到多个身份证号，请检查姓名和单位是否正确");
                } else if (idCardByNameAndOrganizationName.size() == 1) {
                    record.setIdCard(idCardByNameAndOrganizationName.get(0).getString("id_card"));
                } else {
                    throw new RuntimeException("未找到身份证号，请检查姓名和单位是否正确");
                }
            }


            // 资格类目
            record.setCategory(category);

            // 具体项目
            if (row.size() > 2 && row.get(2) != null) {
                record.setProject(row.get(2).toString().trim());
            }

            // 记录日期
            if (row.size() > 3 && row.get(3) != null) {
                try {
                    record.setIssueDate(DateUtil.parse(row.get(3).toString()).toLocalDateTime());
                } catch (Exception e) {
                    log.warn("第{}行记录日期格式错误: {}", rowNum, row.get(3));
                }
            }
            // 成绩
            if (row.size() > 4 && row.get(4) != null) {
                record.setScore(row.get(4).toString().trim());
            }

        } catch (Exception e) {
            throw new RuntimeException("数据格式错误: " + e.getMessage());
        }

        return record;
    }

    public List<PoliceQualificationRecord> queryLatestRecord(PoliceQualificationRecordRequestDTO requestDTO) {
        return baseMapper.queryLatestRecord(requestDTO);
    }


    //    @EventListener(ApplicationReadyEvent.class)
    public void cleanOrganizationId() {
        this.list().forEach(record -> {
            String idCard = record.getIdCard();
            record.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(idCard));
            this.updateById(record);
        });
    }


    public void importPhysical(String fileId) {
        FileDetail fileInfo = fileDetailService.getById(fileId);
        if (fileInfo == null) {
            throw new RuntimeException("文件不存在");
        }

        FileInfo file = fileStorageService.getFileInfoByUrl(fileInfo.getUrl());
        List<PoliceQualificationRecord> recordList = new ArrayList<>();
        fileStorageService.download(file).inputStream(inputStream -> {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> readList = reader.read();
            for (int i = 1; i < readList.size(); i++) {
                List<Object> read = readList.get(i);
                String organization = read.get(0).toString();
                String name = read.get(1).toString();
                if (StrUtil.isBlank(name)) {
                    continue;
                }
                name = ReUtil.replaceAll(name, "（.*?）", "").replaceAll(" ", "");
                List<JSONObject> userInfo = SsoCacheUtil.getIdCardByName(name);
                if (userInfo.size() != 1) {
                    if (userInfo.isEmpty()) {
                        log.error("没有找到用户:{}", name);
                        continue;
                    }
                }
                String twoThousandMeter = read.get(4).toString();
                String twoThousandMeterGrades = read.get(5).toString();
                if (StrUtil.isNotBlank(twoThousandMeter) && StrUtil.isNotBlank(twoThousandMeterGrades)) {
                    PoliceQualificationRecord qualificationRecord = new PoliceQualificationRecord();
                    qualificationRecord.setOrganizationId(userInfo.get(0).getByPath("organization[0].organization_id").toString());
                    qualificationRecord.setIdCard(userInfo.get(0).getString("id_card"));
                    qualificationRecord.setCategory("体能");
                    qualificationRecord.setProject("2000米");
                    twoThousandMeter = twoThousandMeter.replaceAll("′", "分").replaceAll("″", "秒");
                    qualificationRecord.setScore(twoThousandMeter);
                    qualificationRecord.setIssueDate(LocalDateTime.now());
                    qualificationRecord.setScoreQualified(twoThousandMeterGrades);
                    recordList.add(qualificationRecord);
                }
                String pullUp = read.get(6).toString();
                String pullUpGrades = read.get(7).toString();
                if (StrUtil.isNotBlank(pullUp) && StrUtil.isNotBlank(pullUpGrades)) {
                    PoliceQualificationRecord qualificationRecord = new PoliceQualificationRecord();
                    qualificationRecord.setOrganizationId(userInfo.get(0).getByPath("organization[0].organization_id").toString());
                    qualificationRecord.setIdCard(userInfo.get(0).getString("id_card"));
                    qualificationRecord.setCategory("体能");
                    qualificationRecord.setProject("引体向上");
                    qualificationRecord.setScore(pullUp);
                    qualificationRecord.setIssueDate(LocalDateTime.now());
                    qualificationRecord.setScoreQualified(pullUpGrades);
                    recordList.add(qualificationRecord);
                }
            }

        });


        if (!recordList.isEmpty()) {
            this.saveBatch(recordList);
            log.info("体能数据导入成功:{}", recordList.size());
        } else {
            throw new HlErrException("没有有效数据");
        }
    }

    public void importPhysicalIgnore(String fileId) {
        FileDetail fileInfo = fileDetailService.getById(fileId);
        if (fileInfo == null) {
            throw new RuntimeException("文件不存在");
        }

        FileInfo file = fileStorageService.getFileInfoByUrl(fileInfo.getUrl());
        List<PoliceQualificationRecord> recordList = new ArrayList<>();
        fileStorageService.download(file).inputStream(inputStream -> {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> readList = reader.read();
            for (int i = 1; i < readList.size(); i++) {
                List<Object> read = readList.get(i);
                String name = read.get(2).toString();
                if (StrUtil.isBlank(name)) {
                    continue;
                }
                name = ReUtil.replaceAll(name, "（.*?）", "").replaceAll(" ", "");
                List<JSONObject> userInfo = SsoCacheUtil.getIdCardByName(name);
                if (userInfo.size() != 1) {
                    if (userInfo.isEmpty()) {
                        log.error("没有找到用户:{}", name);
                        continue;
                    }
                }
                String ignoreProject = read.get(3).toString();
                if (StrUtil.isNotBlank(ignoreProject)) {
                    String[] split = ignoreProject.split("、");
                    for (String s : split) {
                        PoliceQualificationRecord qualificationRecord = new PoliceQualificationRecord();
                        qualificationRecord.setOrganizationId(userInfo.get(0).getByPath("organization[0].organization_id").toString());
                        qualificationRecord.setIdCard(userInfo.get(0).getString("id_card"));
                        qualificationRecord.setCategory("体能");
                        if ("跑步".equals(s)) {
                            s = "2000米";
                        } else if ("引体向上".equals(s)) {
                            s = "引体向上";
                        } else {
                            continue;
                        }
                        qualificationRecord.setProject(s);
                        qualificationRecord.setScore("免测");
                        qualificationRecord.setIssueDate(LocalDateTime.now());
                        qualificationRecord.setScoreQualified("免测");
                        qualificationRecord.setRemark(read.get(5).toString());
                        recordList.add(qualificationRecord);
                    }
                }
                String slowTest = read.get(4).toString();
                if (StrUtil.isNotBlank(slowTest)) {
                    String[] slow = slowTest.split(",");
                    for (String s : slow) {
                        PoliceQualificationRecord qualificationRecord = new PoliceQualificationRecord();
                        qualificationRecord.setOrganizationId(userInfo.get(0).getByPath("organization[0].organization_id").toString());
                        qualificationRecord.setIdCard(userInfo.get(0).getString("id_card"));
                        qualificationRecord.setCategory("体能");
                        qualificationRecord.setProject(s);
                        qualificationRecord.setScore("缓测");
                        qualificationRecord.setIssueDate(LocalDateTime.now());
                        qualificationRecord.setScoreQualified("缓测");
                        qualificationRecord.setRemark(read.get(5).toString());
                        recordList.add(qualificationRecord);
                    }
                }
            }

        });


        if (!recordList.isEmpty()) {
            this.saveBatch(recordList);
            log.info("体能数据导入成功:{}", recordList.size());
        } else {
            throw new HlErrException("没有有效数据");
        }
    }

    public void importExam(String fileId, Integer type) {
        FileDetail fileInfo = fileDetailService.getById(fileId);
        if (fileInfo == null) {
            throw new RuntimeException("文件不存在");
        }

        FileInfo file = fileStorageService.getFileInfoByUrl(fileInfo.getUrl());
        List<PoliceQualificationRecord> recordList = new ArrayList<>();
        fileStorageService.download(file).inputStream(inputStream -> {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> readList = reader.read();
            for (int i = 1; i < readList.size(); i++) {
                List<Object> read = readList.get(i);
                String policeNumber = read.get(1).toString();
                if (StrUtil.isBlank(policeNumber)) {
                    continue;
                }
                Object userInfo = SsoCacheUtil.getUserObjByPoliceId(policeNumber);
                if (userInfo == null) {
                    log.error("不存在：{}", policeNumber);
                    continue;
                }
                JSONObject user = JSONObject.from(userInfo);

                String score = read.get(4).toString();

                PoliceQualificationRecord qualificationRecord = new PoliceQualificationRecord();
                qualificationRecord.setOrganizationId(user.getByPath("organization[0].organization_id").toString());
                qualificationRecord.setIdCard(user.getString("id_card"));
                qualificationRecord.setCategory("考试");
                if (type == 1) {
                    qualificationRecord.setProject("信息化");
                }
                if (type == 2) {
                    qualificationRecord.setProject("法律");
                }
                qualificationRecord.setScore(score);
                qualificationRecord.setIssueDate(LocalDateTime.now());
                QualificationRecordStrategy strategy = qualificationRecordStrategyFactory.getStrategy(qualificationRecord.getProject());
                qualificationRecord.setScoreQualified(strategy.isPass(strategy.calculatePoint(qualificationRecord.getIdCard(), qualificationRecord.getScore())) ? "合格" : "不合格");
                recordList.add(qualificationRecord);
            }

        });


        if (!recordList.isEmpty()) {
            this.saveBatch(recordList);
            log.info("考试数据导入成功:{}", recordList.size());
        } else {
            throw new HlErrException("没有有效数据");
        }
    }

    /**
     * 获取体能和考试都合格的人员身份证号列表
     * @param requestDTO 查询条件
     * @return 符合条件的身份证号列表
     */
    public List<String> getQualifiedIdCards(PoliceQualificationRecordRequestDTO requestDTO) {
        return baseMapper.getQualifiedIdCards(requestDTO);
    }
}
