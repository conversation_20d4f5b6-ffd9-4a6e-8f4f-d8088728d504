package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import co.elastic.clients.elasticsearch._types.analysis.IcuFoldingTokenFilter;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.AbilityTagStatisticsDTO;
import com.hl.archive.domain.dto.AbilityTagStatisticsQueryDTO;
import com.hl.archive.domain.entity.FileDetail;
import com.hl.archive.domain.entity.PoliceAbilityTag;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.config.PoliceAbilityTagConfig;
import com.hl.archive.listener.event.PoliceAbilityTagDeleteEvent;
import com.hl.archive.listener.event.PoliceAbilityTagEvent;
import com.hl.archive.mapper.PoliceAbilityTagMapper;
import com.hl.archive.utils.AbilityDictUtils;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import com.hl.dict.DictCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.tika.ContentTypeDetect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 民警职业能力标签服务接口
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PoliceAbilityTagService extends ServiceImpl<PoliceAbilityTagMapper, PoliceAbilityTag> {

    @Value("${spring.security.sso.projectToken}")
    private String token;

    @Resource
    private TaskApi taskApi;

    private final PoliceAbilityTagConfig policeAbilityTagConfig;

    private final FileStorageService fileStorageService;

    private final FileDetailService fileDetailService;

    @Resource(name = "datasource2DataSource")
    private DataSource taskDataSource;
    @Autowired
    private ContentTypeDetect contentTypeDetect;

    @EventListener(PoliceAbilityTagEvent.class)
    public void parseTaskAbilityTag(PoliceAbilityTagEvent event) {
        try {
            JSONObject taskData = event.getTaskData();
            log.info("职业能力标签:{}",taskData);
            String opt = taskData.getByPath("opt").toString();
            if (!"audit".equals(opt)){
                return;
            }
            String passStatus= taskData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)){
                return;
            }
            // 处理内容数据
            handleContentData(taskData);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    private void handleContentData(JSONObject taskData) {
        String taskId = taskData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        JSONObject content = parsed.getJSONObject("all_content");
        List<String> abilityTagCode = content.getList(policeAbilityTagConfig.getAbilityTagCode(), String.class);
        List<String> abilityTagName = new ArrayList<>();
        for (String tagCode : abilityTagCode) {
            if (DictCache.ID_MAP_TREE.containsKey(tagCode)) {
                String dictName = DictCache.ID_MAP_TREE.getJSONObject(tagCode).getString("name");
                abilityTagName.add(dictName);
            }
        }
        List<String> idCard = content.getList(policeAbilityTagConfig.getIdCard(), String.class);
        String description = content.getString(policeAbilityTagConfig.getDescription());
        String obtainTime = content.getString(policeAbilityTagConfig.getObtainTime());
        for (String s : abilityTagCode) {
            for (String i : idCard) {
                PoliceAbilityTag policeAbilityTag = new PoliceAbilityTag();
                if (DictCache.ID_MAP_TREE.containsKey(s)) {
                    String dictName = DictCache.ID_MAP_TREE.getJSONObject(s).getString("name");
                    policeAbilityTag.setAbilityTagName(dictName);
                }
                policeAbilityTag.setAbilityTagCode(s);
                policeAbilityTag.setIdCard(i);
                policeAbilityTag.setDescription(description);
                policeAbilityTag.setObtainTime(DateUtil.parse(obtainTime).toLocalDateTime().toLocalDate());
                policeAbilityTag.setTaskId(taskId);
                log.info("任务:{} 审批通过,新增职业能力标签:{}",taskId,policeAbilityTag);
                this.save(policeAbilityTag);
            }
        }


    }


    @EventListener(PoliceAbilityTagDeleteEvent.class)
    public void handleDeleteEvent(PoliceAbilityTagDeleteEvent event) {
        String taskId = null;
        try {
            JSONObject taskData = event.getTaskData();
            taskId = taskData.getByPath("data.task_id").toString();
            log.info("任务:{} 删除职业能力标签", taskId);
            this.remove(Wrappers.<PoliceAbilityTag>lambdaQuery()
                    .eq(PoliceAbilityTag::getTaskId, taskId));
        } catch (Exception e) {
            log.info("任务:{} 删除职业能力标签失败", taskId);
            log.error(e.getMessage(), e);
        }
    }


    @JobExecutor(name = "handleAbilityTag")
    public void handleAbilityTag(JobArgs jobArgs) {
        try {
            Object jobParams = jobArgs.getJobParams();
            if (jobParams == null) {
                SnailJobLog.REMOTE.info("职业能力标签任务参数为空");
                return;
            }
            JSONObject param = JSONObject.parseObject(jobParams.toString());
            List<String> taskId = param.getList("taskId", String.class);
            if (taskId == null || taskId.isEmpty()) {
                SnailJobLog.REMOTE.info("职业能力标签任务id为空");
                return;
            }
            String taskStr = taskId.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            List<Entity> query = DbUtil.use(taskDataSource).query("select * from view_archive_task_event where opt = 'audit' and task_id in (?) ", taskStr);
            for (Entity entity : query) {
                String contentData = entity.getStr("content_data");
                JSONObject contentDataObj = JSONObject.parseObject(contentData);
                SnailJobLog.REMOTE.info("职业能力补偿任务处理:{}", contentDataObj);
                handleContentData(contentDataObj);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }


    @JobExecutor(name = "cleanTagInfo")
    public void cleanTagInfo(){
        List<PoliceAbilityTag> list = this.list();
        for (PoliceAbilityTag policeAbilityTag : list) {
            String abilityTagCode = policeAbilityTag.getAbilityTagCode();
            String abilityTagName = policeAbilityTag.getAbilityTagName();
            String idCard = policeAbilityTag.getIdCard();
            policeAbilityTag.setAbilityTagCode(abilityTagCode.replaceAll("^\\[\"|\"\\]$", ""));
            policeAbilityTag.setAbilityTagName(abilityTagName.replaceAll("^\\[\"|\"\\]$", ""));
            policeAbilityTag.setIdCard(idCard.replaceAll("^\\[\"|\"\\]$", ""));
            this.updateById(policeAbilityTag);
        }
    }


    @JobExecutor(name = "cleanTagOrganizationId")
    public void cleanOrganizationId(){
        List<PoliceAbilityTag> list = this.list();
        for (PoliceAbilityTag policeAbilityTag : list) {
            policeAbilityTag.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(policeAbilityTag.getIdCard()));
            this.updateById(policeAbilityTag);
        }
    }

    /**
     * 根据能力标签代码进行动态层级统计（数据库层统计）
     */
    public List<AbilityTagStatisticsDTO> getAbilityTagStatistics(AbilityTagStatisticsQueryDTO request) {
        // 调用数据库层统计查询
        List<Map<String, Object>> statisticsData = baseMapper.getAbilityTagStatistics(request);

        // 构建树形结构的根节点映射
        Map<String, AbilityTagStatisticsDTO> rootMap = new LinkedHashMap<>();

        // 处理查询结果
        for (Map<String, Object> row : statisticsData) {
            String abilityTagCode = (String) row.get("ability_tag_code");
            String abilityTagName = (String) row.get("ability_tag_name");
            Integer count = ((Number) row.get("count")).intValue();

            // 通过字典代码获取完整层级路径
            List<String> levelPath = AbilityDictUtils.getLevelNamePath(abilityTagCode);

            // 如果无法获取层级信息，跳过
            if (levelPath.isEmpty()) {
                log.warn("无法获取字典代码 {} 的层级信息", abilityTagCode);
                continue;
            }

            // 应用层级路径筛选
            if (!AbilityDictUtils.matchesLevelPath(abilityTagCode, request.getLevelPath())) {
                continue;
            }

            // 应用分类名称筛选
            if (request.getCategoryName() != null &&
                !levelPath.stream().anyMatch(name -> name.contains(request.getCategoryName()))) {
                continue;
            }

            // 构建或更新树形结构
            buildTreeStructure(rootMap, levelPath, abilityTagCode, abilityTagName, count, request.getMaxLevel());
        }

        // 转换为列表并排序
        List<AbilityTagStatisticsDTO> resultList = new ArrayList<>(rootMap.values());

        // 递归排序所有层级
        sortTreeRecursively(resultList);

        return resultList;
    }

    /**
     * 构建树形结构
     */
    private void buildTreeStructure(Map<String, AbilityTagStatisticsDTO> rootMap,
                                   List<String> levelPath,
                                   String dictCode,
                                   String abilityTagName,
                                   Integer count,
                                   Integer maxLevel) {

        Map<String, AbilityTagStatisticsDTO> currentLevelMap = rootMap;
        AbilityTagStatisticsDTO parentNode = null;

        // 遍历层级路径，构建树形结构
        for (int i = 0; i < levelPath.size(); i++) {
            String categoryName = levelPath.get(i);

            // 检查是否超过最大层级限制
            if (maxLevel != null && i >= maxLevel) {
                break;
            }

            // 获取或创建当前层级节点
            AbilityTagStatisticsDTO currentNode = currentLevelMap.get(categoryName);
            if (currentNode == null) {
                currentNode = new AbilityTagStatisticsDTO();
                currentNode.setCategoryName(categoryName);
                currentNode.setCount(0);
                currentNode.setLevel(i);
                currentNode.setLevelPath(new ArrayList<>(levelPath.subList(0, i + 1)));
                currentNode.setChildren(new ArrayList<>());
                currentNode.setIsLeaf(false);

                currentLevelMap.put(categoryName, currentNode);

                // 如果有父节点，添加到父节点的子列表中
                if (parentNode != null) {
                    parentNode.getChildren().add(currentNode);
                }
            }

            // 累加人数
            currentNode.setCount(currentNode.getCount() + count);

            // 如果是最后一层或达到最大层级，设置为叶子节点
            if (i == levelPath.size() - 1 || (maxLevel != null && i == maxLevel - 1)) {
                currentNode.setIsLeaf(true);
                currentNode.setDictCode(dictCode);
                // 如果当前节点的children为空，初始化为空列表而不是null
                if (currentNode.getChildren() == null) {
                    currentNode.setChildren(new ArrayList<>());
                }
            } else {
                // 准备下一层级的映射
                if (currentNode.getChildren() == null) {
                    currentNode.setChildren(new ArrayList<>());
                }

                // 为下一层级准备映射
                Map<String, AbilityTagStatisticsDTO> nextLevelMap = new LinkedHashMap<>();
                for (AbilityTagStatisticsDTO child : currentNode.getChildren()) {
                    nextLevelMap.put(child.getCategoryName(), child);
                }
                currentLevelMap = nextLevelMap;
            }

            parentNode = currentNode;
        }
    }

    /**
     * 递归排序树形结构
     */
    private void sortTreeRecursively(List<AbilityTagStatisticsDTO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        // 按人数降序排序
        nodes.sort((a, b) -> b.getCount().compareTo(a.getCount()));

        // 递归排序子节点
        for (AbilityTagStatisticsDTO node : nodes) {
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                sortTreeRecursively(node.getChildren());
            }
        }
    }



    public void importExcel(String fileId){
        FileDetail fileInfo = fileDetailService.getById(fileId);
        if (fileInfo == null) {
            throw new RuntimeException("文件不存在");
        }

        FileInfo file = fileStorageService.getFileInfoByUrl(fileInfo.getUrl());

        Map<String,String> dict = new HashMap<>();
        dict.put("基本级","1943153462615240706");
        dict.put("中级","17588728748472256268");
        dict.put("高级","1943153463189860353");

        fileStorageService.download(file).inputStream(inputStream -> {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<Map<String, Object>> mapList = reader.readAll();

            for (Map<String, Object> map : mapList) {
                String idCard = map.get("身份证号码").toString();
                if (StrUtil.isNotBlank(idCard)) {
                    Object userObjByIdCard = SsoCacheUtil.getUserObjByIdCard(idCard);
                    if (userObjByIdCard != null) {
                        String ability = map.get("资格等级").toString();
                        String code = dict.get(ability);
                        PoliceAbilityTag one = this.getOne(Wrappers.<PoliceAbilityTag>lambdaQuery()
                                .eq(PoliceAbilityTag::getIdCard, idCard)
                                .eq(PoliceAbilityTag::getAbilityTagCode, code));
                        if (one != null) {
                            continue;
                        }
                        PoliceAbilityTag abilityTag = new PoliceAbilityTag();
                        abilityTag.setIdCard(idCard);
                        abilityTag.setAbilityTagCode(code);
                        abilityTag.setAbilityTagName("执法资格（"+ability+"）");
                        abilityTag.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(idCard));
                        this.save(abilityTag);
                    }
                }
            }
        });
    }
}
