package com.hl.archive.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.TaskTodoQueryDTO;
import com.hl.archive.enums.TaskConfigEnum;
import com.hl.common.config.datasource.DataSource;
import com.hl.common.config.datasource.DataSourceType;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.ViewArchiveTaskTodoMapper;
import com.hl.archive.domain.entity.ViewArchiveTaskTodo;
@Service
@DataSource(DataSourceType.DATASOURCE2)
public class ViewArchiveTaskTodoService extends ServiceImpl<ViewArchiveTaskTodoMapper, ViewArchiveTaskTodo> {



    public Page<ViewArchiveTaskTodo> pageList(TaskTodoQueryDTO queryDTO){
        Page<ViewArchiveTaskTodo> page = page(Page.of(queryDTO.getPage(), queryDTO.getLimit()),
                Wrappers.<ViewArchiveTaskTodo>lambdaQuery()
                        .eq(ViewArchiveTaskTodo::getStatusIdCard, queryDTO.getIdCard()));
        if (!page.getRecords().isEmpty()) {
           page.getRecords().forEach(viewArchiveTaskTodo -> {
               TaskConfigEnum taskConfigEnum = TaskConfigEnum.getTaskConfigEnum(viewArchiveTaskTodo.getConfigUuid());
               if (taskConfigEnum != null) {
                   viewArchiveTaskTodo.setConfigName(taskConfigEnum.getConfigName());
               }
           });
        }

        return page;
    }

}
